# 感为无MCU八路灰度传感器接线说明与问题分析

## 1. 传感器概述

感为无MCU八路灰度传感器是一款基于模拟多路复用器的循迹传感器，通过3位地址线控制8路传感器的切换，使用单路ADC即可读取所有传感器数据。

### 传感器特性
- **8路红外传感器**: 支持黑白线检测
- **模拟输出**: 提供连续的灰度值
- **数字化处理**: 内置阈值比较，支持数字量输出
- **地址复用**: 3位地址线控制8路传感器切换
- **校准功能**: 支持黑白值校准和归一化处理

## 2. 硬件接线说明

### 2.1 引脚配置（基于您的代码配置）

| 功能 | MCU引脚 | 传感器引脚 | 说明 |
|------|---------|------------|------|
| **电源** | 3.3V | VCC | 逻辑电源 |
| **地线** | GND | GND | 公共地 |
| **ADC输入** | PA15 | OUT | 模拟信号输出 |
| **地址线0** | PB18 | A0 | 地址位0控制 |
| **地址线1** | PB15 | A1 | 地址位1控制 |
| **地址线2** | PB16 | A2 | 地址位2控制 |

### 2.2 详细接线表

```
传感器端    →    MCU端(MSPM0G3507)
VCC        →    3.3V
GND        →    GND
OUT        →    PA15 (ADC1_CH0)
A0         →    PB18 (Gray_Address_PIN_0)
A1         →    PB15 (Gray_Address_PIN_1) 
A2         →    PB16 (Gray_Address_PIN_2)
```

### 2.3 传感器排列说明

8路传感器从左到右编号为0-7，对应的地址线组合：
- 传感器0: A2=0, A1=0, A0=0 (000)
- 传感器1: A2=0, A1=0, A0=1 (001)
- 传感器2: A2=0, A1=1, A0=0 (010)
- 传感器3: A2=0, A1=1, A0=1 (011)
- 传感器4: A2=1, A1=0, A0=0 (100)
- 传感器5: A2=1, A1=0, A0=1 (101)
- 传感器6: A2=1, A1=1, A0=0 (110)
- 传感器7: A2=1, A1=1, A0=1 (111)

## 3. 软件配置分析

### 3.1 ADC配置
- **ADC实例**: ADC1
- **分辨率**: 12位 (0-4095)
- **参考电压**: VDDA (3.3V)
- **采样通道**: PA15 (ADC1_CH0)

### 3.2 GPIO配置
```c
// 地址线控制宏定义
#define Switch_Address_0(i) ((i)?(DL_GPIO_setPins(Gray_Address_PORT,Gray_Address_PIN_0_PIN)) : (DL_GPIO_clearPins(Gray_Address_PORT,Gray_Address_PIN_0_PIN)))
#define Switch_Address_1(i) ((i)?(DL_GPIO_setPins(Gray_Address_PORT,Gray_Address_PIN_1_PIN)) : (DL_GPIO_clearPins(Gray_Address_PORT,Gray_Address_PIN_1_PIN)))
#define Switch_Address_2(i) ((i)?(DL_GPIO_setPins(Gray_Address_PORT,Gray_Address_PIN_2_PIN)) : (DL_GPIO_clearPins(Gray_Address_PORT,Gray_Address_PIN_2_PIN)))
```

## 4. 移植问题分析

### 4.1 ✅ 已解决: 传感器实例和初始化
**状态**: 已在Task_App.c中创建GraySensor实例并完成初始化
- 使用了预设的黑白校准值 (白:1800, 黑:300)
- 传感器配置为12位ADC模式

### 4.2 ⚠️ 关键问题1: 缺少GraySensor变量声明
**问题**: Task_App.c中使用了`GraySensor`变量，但未在头文件中声明。

**解决方案**: 需要在Task_App.h中添加声明：
```c
extern No_MCU_Sensor GraySensor; // 灰度传感器实例
```

### 4.3 ⚠️ 关键问题2: 缺少Task_GraySensor函数定义
**问题**: Task_Init()中添加了"GraySensor"任务，但未定义Task_GraySensor函数。

**解决方案**: 需要在Task_App.c中添加函数定义：
```c
void Task_GraySensor(void *para)
{
    // 执行传感器数据采集
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
}
```

### 4.4 ⚠️ 关键问题3: Task_Tracker中的未定义变量
**问题**: Task_Tracker函数中使用了未定义的变量：
- `Gray_Digtal` (应该是`GraySensor.Digtal`)
- `DIS_INTERVAL` (应该是传感器间距常量)

**解决方案**: 修正变量引用：
```c
// 修改前
Data_Tracker_Input[i] = (Gray_Digtal >> i) & 0x01;
_iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INTERVAL);

// 修改后
Data_Tracker_Input[i] = (GraySensor.Digtal >> i) & 0x01;
_iq sensor_pos = _IQmpy(_IQ(i - 3.5f), _IQ(1.5)); // 1.5cm间距
```

### 4.5 ⚠️ 关键问题4: 函数声明缺失
**问题**: Task_GraySensor函数未在头文件中声明。

**解决方案**: 在Task_App.h中添加声明：
```c
void Task_GraySensor(void *para); // 灰度传感器任务
```

### 4.6 ⚠️ 关键问题5: 地址线逻辑取反
**问题**: 代码中使用`!(i&0x01)`等取反逻辑，可能与传感器实际需求不符。

**检查方法**: 测试时观察传感器切换是否正确。

## 5. 必要的代码修复

### 5.1 在Task_App.h中添加声明
```c
// 在Task_App.h中添加以下声明
extern No_MCU_Sensor GraySensor; // 灰度传感器实例
void Task_GraySensor(void *para); // 灰度传感器任务
```

### 5.2 在Task_App.c中添加变量定义和函数实现
```c
// 在Task_App.c文件顶部添加全局变量定义
No_MCU_Sensor GraySensor; // 灰度传感器实例

// 添加Task_GraySensor函数实现
void Task_GraySensor(void *para)
{
    // 执行传感器数据采集和处理
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
}
```

### 5.3 修复Task_Tracker函数中的变量引用
```c
// 修改Task_Tracker函数中的错误引用
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); //滤波系数
    _iq Temp = 0;
    bool res = false;

    if (GraySensor.ok) // 传感器初始化完成
    {
        // 将灰度数字值转换为循迹输入（8位）
        for (uint8_t i = 0; i < 8; i++)
        {
            Data_Tracker_Input[i] = (GraySensor.Digtal >> i) & 0x01; // 修正变量名
        }
        // 计算偏差（复用原有加权算法）
        _iq pos_sum = _IQ(0);
        uint8_t cnt = 0;
        for (uint8_t i = 0; i < 8; i++)
        {
            if (Data_Tracker_Input[i] == TRACK_ON)
            {
                _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), _IQ(1.5)); // 修正常量
                pos_sum += sensor_pos;
                cnt++;
            }
        }
        if (cnt > 0)
        {
            Temp = _IQdiv(pos_sum, _IQ(cnt));
            res = true;
        }
    }

    // 原有滤波逻辑不变
    if (res == true)
    {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}
```

### 5.4 启用循迹任务
```c
// 在Task_Init()中取消注释
Task_Add("Tracker", Task_Tracker, 20, NULL, 1);
```

## 6. 调试建议

### 6.1 ADC测试函数
```c
// 添加ADC调试函数到Task_App.c
void Debug_ADC_Values(void)
{
    MyPrintf_DMA("=== ADC Values Test ===\r\n");
    for(int i = 0; i < 8; i++) {
        Switch_Address_0(!(i&0x01));
        Switch_Address_1(!(i&0x02));
        Switch_Address_2(!(i&0x04));

        uint16_t adc_val = adc_getValue();
        MyPrintf_DMA("Sensor[%d]: %d\r\n", i, adc_val);
        // 添加小延时确保地址切换稳定
        for(volatile int j = 0; j < 1000; j++);
    }
    MyPrintf_DMA("===================\r\n");
}
```

### 6.2 传感器状态监控
```c
// 添加传感器状态调试函数
void Debug_Sensor_Status(void)
{
    if(GraySensor.ok) {
        uint8_t digital = Get_Digtal_For_User(&GraySensor);
        MyPrintf_DMA("Digital: 0x%02X, Binary: ", digital);
        for(int i = 7; i >= 0; i--) {
            MyPrintf_DMA("%d", (digital >> i) & 1);
        }
        MyPrintf_DMA(", Offset: %.2f\r\n", _IQtoF(Data_Tracker_Offset));
    } else {
        MyPrintf_DMA("Sensor not ready!\r\n");
    }
}
```

### 6.3 完整的传感器调试任务
```c
// 可以替换Task_GraySensor为调试版本
void Task_GraySensor_Debug(void *para)
{
    static uint16_t debug_cnt = 0;

    // 执行传感器数据采集
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);

    // 每100次输出一次调试信息 (1秒一次，假设10ms调用周期)
    if(++debug_cnt >= 100) {
        debug_cnt = 0;
        Debug_Sensor_Status();

        // 输出原始ADC值
        MyPrintf_DMA("Raw ADC: ");
        for(int i = 0; i < 8; i++) {
            MyPrintf_DMA("%d ", GraySensor.Analog_value[i]);
        }
        MyPrintf_DMA("\r\n");
    }
}
```

## 7. 编译错误解决

### 7.1 可能的编译错误
1. **未定义的变量**: `GraySensor`、`Gray_Digtal`、`DIS_INTERVAL`
2. **未声明的函数**: `Task_GraySensor`
3. **头文件包含**: 确保包含了`No_Mcu_Ganv_Grayscale_Sensor_Config.h`

### 7.2 解决步骤
按照第5节的修复步骤依次添加缺失的声明和定义。

## 8. 注意事项

1. **电源稳定性**: 确保3.3V电源稳定，避免ADC读数波动
2. **地线连接**: 确保传感器与MCU共地
3. **传感器高度**: 调整传感器与地面距离(建议2-8mm)
4. **环境光影响**: 避免强光直射影响检测精度
5. **校准重要性**: 首次使用必须进行黑白校准
6. **采样频率**: 建议10-20ms周期读取，避免过快采样
7. **地址切换延时**: 地址线切换后需要适当延时确保稳定
8. **ADC参考电压**: 确保ADC参考电压与传感器供电电压一致

## 9. 预期效果

正确接线和修复代码后，传感器应能：
- 检测黑线位置并输出偏差值(-5.25cm ~ +5.25cm)
- 数字量输出正确反映黑白状态(0=黑线, 1=白色)
- ADC值在校准范围内变化(黑:300, 白:1800)
- 循迹控制响应及时准确
- 串口输出调试信息正常显示

## 10. 故障排除

| 问题现象 | 可能原因 | 解决方法 |
|---------|---------|---------|
| 编译错误 | 缺少声明/定义 | 按第5节修复代码 |
| ADC值异常 | 接线错误/电源问题 | 检查接线和电源 |
| 传感器无响应 | 地址线控制错误 | 检查地址线逻辑 |
| 数字量全0或全1 | 校准值不当 | 重新校准黑白值 |
| 偏差计算错误 | 传感器间距设置 | 确认1.5cm间距设置 |
