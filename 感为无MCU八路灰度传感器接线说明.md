# 感为无MCU八路灰度传感器接线说明与问题分析

## 1. 传感器概述

感为无MCU八路灰度传感器是一款基于模拟多路复用器的循迹传感器，通过3位地址线控制8路传感器的切换，使用单路ADC即可读取所有传感器数据。

### 传感器特性
- **8路红外传感器**: 支持黑白线检测
- **模拟输出**: 提供连续的灰度值
- **数字化处理**: 内置阈值比较，支持数字量输出
- **地址复用**: 3位地址线控制8路传感器切换
- **校准功能**: 支持黑白值校准和归一化处理

## 2. 硬件接线说明

### 2.1 引脚配置（基于您的代码配置）

| 功能 | MCU引脚 | 传感器引脚 | 说明 |
|------|---------|------------|------|
| **电源** | 3.3V | VCC | 逻辑电源 |
| **地线** | GND | GND | 公共地 |
| **ADC输入** | PA15 | OUT | 模拟信号输出 |
| **地址线0** | PB18 | A0 | 地址位0控制 |
| **地址线1** | PB15 | A1 | 地址位1控制 |
| **地址线2** | PB16 | A2 | 地址位2控制 |

### 2.2 详细接线表

```
传感器端    →    MCU端(MSPM0G3507)
VCC        →    3.3V
GND        →    GND
OUT        →    PA15 (ADC1_CH0)
A0         →    PB18 (Gray_Address_PIN_0)
A1         →    PB15 (Gray_Address_PIN_1) 
A2         →    PB16 (Gray_Address_PIN_2)
```

### 2.3 传感器排列说明

8路传感器从左到右编号为0-7，对应的地址线组合：
- 传感器0: A2=0, A1=0, A0=0 (000)
- 传感器1: A2=0, A1=0, A0=1 (001)
- 传感器2: A2=0, A1=1, A0=0 (010)
- 传感器3: A2=0, A1=1, A0=1 (011)
- 传感器4: A2=1, A1=0, A0=0 (100)
- 传感器5: A2=1, A1=0, A0=1 (101)
- 传感器6: A2=1, A1=1, A0=0 (110)
- 传感器7: A2=1, A1=1, A0=1 (111)

## 3. 软件配置分析

### 3.1 ADC配置
- **ADC实例**: ADC1
- **分辨率**: 12位 (0-4095)
- **参考电压**: VDDA (3.3V)
- **采样通道**: PA15 (ADC1_CH0)

### 3.2 GPIO配置
```c
// 地址线控制宏定义
#define Switch_Address_0(i) ((i)?(DL_GPIO_setPins(Gray_Address_PORT,Gray_Address_PIN_0_PIN)) : (DL_GPIO_clearPins(Gray_Address_PORT,Gray_Address_PIN_0_PIN)))
#define Switch_Address_1(i) ((i)?(DL_GPIO_setPins(Gray_Address_PORT,Gray_Address_PIN_1_PIN)) : (DL_GPIO_clearPins(Gray_Address_PORT,Gray_Address_PIN_1_PIN)))
#define Switch_Address_2(i) ((i)?(DL_GPIO_setPins(Gray_Address_PORT,Gray_Address_PIN_2_PIN)) : (DL_GPIO_clearPins(Gray_Address_PORT,Gray_Address_PIN_2_PIN)))
```

## 4. 移植问题分析

### 4.1 ⚠️ 关键问题1: 函数名不匹配
**问题**: Task_App.c中调用`Tracker_Read()`函数，但Tracker.c中实现的是`GrayscaleTracker_Read()`函数。

**解决方案**:
```c
// 在Tracker.h中添加兼容性函数声明
bool Tracker_Read(uint8_t *tck_data, _iq *offset_ptr);

// 在Tracker.c中添加兼容性函数实现
bool Tracker_Read(uint8_t *tck_data, _iq *offset_ptr)
{
    // 需要创建传感器实例
    static No_MCU_Sensor sensor;
    static bool sensor_initialized = false;
    
    if (!sensor_initialized) {
        // 初始化传感器（需要校准数据）
        No_MCU_Ganv_Sensor_Init_Frist(&sensor);
        sensor_initialized = true;
    }
    
    return GrayscaleTracker_Read(&sensor, tck_data, offset_ptr);
}
```

### 4.2 ⚠️ 关键问题2: 传感器实例未创建
**问题**: 新的灰度传感器需要`No_MCU_Sensor`结构体实例，但代码中未创建。

**解决方案**: 需要在Task_App.c中创建传感器实例并进行初始化。

### 4.3 ⚠️ 关键问题3: 校准数据缺失
**问题**: 传感器需要黑白校准数据才能正常工作，但代码中未提供。

**解决方案**: 需要添加校准程序或使用默认校准值。

### 4.4 ⚠️ 关键问题4: 地址线逻辑取反
**问题**: 代码中使用`!(i&0x01)`等取反逻辑，可能与传感器实际需求不符。

**检查方法**: 测试时观察传感器切换是否正确。

## 5. 建议的修复步骤

### 5.1 添加兼容性函数
```c
// 在Tracker.c中添加
extern No_MCU_Sensor Gray_Sensor; // 全局传感器实例

bool Tracker_Read(uint8_t *tck_data, _iq *offset_ptr)
{
    // 执行传感器任务
    No_Mcu_Ganv_Sensor_Task_Without_tick(&Gray_Sensor);
    
    // 调用新的读取函数
    return GrayscaleTracker_Read(&Gray_Sensor, tck_data, offset_ptr);
}
```

### 5.2 初始化传感器实例
```c
// 在Task_App.c中添加
No_MCU_Sensor Gray_Sensor;

// 在Task_Init()中添加
void Task_Init(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化灰度传感器
    No_MCU_Ganv_Sensor_Init_Frist(&Gray_Sensor);
    
    // 如果有校准数据，使用完整初始化
    // unsigned short white_cal[8] = {3000, 3000, 3000, 3000, 3000, 3000, 3000, 3000};
    // unsigned short black_cal[8] = {500, 500, 500, 500, 500, 500, 500, 500};
    // No_MCU_Ganv_Sensor_Init(&Gray_Sensor, white_cal, black_cal);
}
```

### 5.3 启用循迹任务
```c
// 在Task_Init()中取消注释
Task_Add("Tracker", Task_Tracker, 20, NULL, 1);
```

## 6. 调试建议

### 6.1 ADC测试
```c
// 添加ADC调试函数
void Debug_ADC_Values(void)
{
    for(int i = 0; i < 8; i++) {
        Switch_Address_0(!(i&0x01));
        Switch_Address_1(!(i&0x02));
        Switch_Address_2(!(i&0x04));
        
        uint16_t adc_val = adc_getValue();
        MyPrintf_DMA("Sensor[%d]: %d\r\n", i, adc_val);
        delay_ms(10);
    }
}
```

### 6.2 传感器状态监控
```c
// 添加传感器状态调试
void Debug_Sensor_Status(void)
{
    uint8_t digital = Get_Digtal_For_User(&Gray_Sensor);
    MyPrintf_DMA("Digital: 0x%02X, Binary: ", digital);
    for(int i = 7; i >= 0; i--) {
        MyPrintf_DMA("%d", (digital >> i) & 1);
    }
    MyPrintf_DMA("\r\n");
}
```

## 7. 注意事项

1. **电源稳定性**: 确保3.3V电源稳定，避免ADC读数波动
2. **地线连接**: 确保传感器与MCU共地
3. **传感器高度**: 调整传感器与地面距离(建议2-8mm)
4. **环境光影响**: 避免强光直射影响检测精度
5. **校准重要性**: 首次使用必须进行黑白校准
6. **采样频率**: 建议20ms周期读取，避免过快采样

## 8. 预期效果

正确接线和配置后，传感器应能：
- 检测黑线位置并输出偏差值
- 数字量输出正确反映黑白状态  
- ADC值在合理范围内变化
- 循迹控制响应及时准确
