# TB6612电机驱动配置指南

## 概述
本文档指导您使用TB6612电机驱动模块替换原有的A4950电机驱动。TB6612已配置完成，本文档提供硬件连接指南。

## A4950 vs TB6612 差异分析

### A4950 (原配置)
- **控制方式**: PWM + 方向控制
- **引脚需求**: 每个电机需要2个引脚 (1个PWM + 1个方向控制)

### TB6612 (当前配置)
- **控制方式**: IN1/IN2方向控制 + PWM调速
- **引脚需求**: 每个电机需要3个引脚 (IN1 + IN2 + PWM)，4路共用1个STBY
- **优势**: 更精确的速度控制，支持制动模式，4路集成驱动

## 硬件连接指南

### TB6612模块引脚分配（基于ti_msp_dl_config.h）

#### 电机PWM调速引脚：
```
左前轮PWM: PA12 (TIMG0_CCP0) - 连接TB6612的PWMA
右前轮PWM: PA13 (TIMG0_CCP1) - 连接TB6612的PWMB
左后轮PWM: PA1  (TIMG8_CCP0) - 连接TB6612的PWMC
右后轮PWM: PA0  (TIMG8_CCP1) - 连接TB6612的PWMD
```

#### 电机方向控制引脚（IN1）：
```
左前轮IN1: PB6  - 连接TB6612的AIN1
右前轮IN1: PB7  - 连接TB6612的BIN1
左后轮IN1: PB8  - 连接TB6612的CIN1
右后轮IN1: PB9  - 连接TB6612的DIN1
```

#### 电机方向控制引脚（IN2）：
```
左前轮IN2: PA15 - 连接TB6612的AIN2
右前轮IN2: PA16 - 连接TB6612的BIN2
左后轮IN2: PA21 - 连接TB6612的CIN2
右后轮IN2: PA22 - 连接TB6612的DIN2
```

#### 使能控制引脚：
```
STBY: PA23 - 连接TB6612的STBY（4路共用使能）
```

### 完整连接表格

| 功能 | MCU引脚 | TB6612引脚 | 说明 |
|------|---------|------------|------|
| 左前轮PWM | PA12 | PWMA | 左前轮调速控制 |
| 左前轮IN1 | PB6 | AIN1 | 左前轮方向控制1 |
| 左前轮IN2 | PA15 | AIN2 | 左前轮方向控制2 |
| 右前轮PWM | PA13 | PWMB | 右前轮调速控制 |
| 右前轮IN1 | PB7 | BIN1 | 右前轮方向控制1 |
| 右前轮IN2 | PA16 | BIN2 | 右前轮方向控制2 |
| 左后轮PWM | PA1 | PWMC | 左后轮调速控制 |
| 左后轮IN1 | PB8 | CIN1 | 左后轮方向控制1 |
| 左后轮IN2 | PA21 | CIN2 | 左后轮方向控制2 |
| 右后轮PWM | PA0 | PWMD | 右后轮调速控制 |
| 右后轮IN1 | PB9 | DIN1 | 右后轮方向控制1 |
| 右后轮IN2 | PA22 | DIN2 | 右后轮方向控制2 |
| 使能控制 | PA23 | STBY | 4路共用使能 |

### 电源连接

| TB6612引脚 | 连接 | 说明 |
|------------|------|------|
| VCC | 3.3V | 逻辑电源 |
| GND | GND | 逻辑地 |
| VM | 电机电源+ | 电机驱动电源(6-15V) |
| GND | 电机电源- | 电机驱动地 |

### 电机连接

| TB6612引脚 | 连接 | 说明 |
|------------|------|------|
| AO1, AO2 | 左前轮电机 | 电机A输出 |
| BO1, BO2 | 右前轮电机 | 电机B输出 |
| CO1, CO2 | 左后轮电机 | 电机C输出 |
| DO1, DO2 | 右后轮电机 | 电机D输出 |

## 配置状态

### ✅ 已完成配置：
1. PWM通道用于调速控制
2. IN1/IN2 GPIO用于方向控制
3. STBY引脚配置（4路共用使能）
4. 引脚分配无冲突
5. 时钟配置一致

### 📋 配置文件状态：
- `ti_msp_dl_config.h` - ✅ 已生成正确的宏定义
- `empty.syscfg` - ✅ 已配置完成
- 引脚映射表 - ✅ 已验证

## TB6612控制逻辑说明

### 电机控制方式：
```
IN1  IN2  PWM   电机状态
0    0    X     停止（制动）
0    1    PWM   正转（调速）
1    0    PWM   反转（调速）
1    1    X     停止（制动）
STBY=0          所有电机停止
```

### 控制优势：
1. **精确方向控制**: IN1/IN2组合提供4种状态
2. **独立调速**: PWM信号独立控制速度
3. **制动功能**: 支持电机制动模式
4. **统一使能**: STBY控制所有电机的总开关

## 代码修改状态

### ✅ 已完成：
- 硬件连接指南
- 引脚分配表
- 配置验证

### 🔄 进行中：
- `BSP/Inc/Motor.h` - 更新电机结构体定义
- `BSP/Src/Motor.c` - 实现TB6612控制逻辑
- README.md更新

### 使用说明

1. **连线**: 按照上述连接表格进行硬件连线
2. **测试**: 连线完成后可直接使用现有代码进行测试
3. **调试**: 如有问题，检查连线和电源供应

## 注意事项

1. **电源要求**: TB6612需要独立的电机电源(6-15V)
2. **电流限制**: 确保电源能提供足够电流
3. **散热**: 大电流工作时注意TB6612散热
4. **保护**: 建议添加电源保护电路
