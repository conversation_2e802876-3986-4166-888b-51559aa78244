******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 18:18:50 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005041


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005e48  0001a1b8  R  X
  SRAM                  20200000   00008000  0000073c  000078c4  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005e48   00005e48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005a10   00005a10    r-x .text
  00005ad0    00005ad0    00000300   00000300    r-- .rodata
  00005dd0    00005dd0    00000078   00000078    r-- .cinit
20200000    20200000    0000053d   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000169   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005a10     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    0000025c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001348    0000022c     MPU6050.o (.text.Read_Quad)
                  00001574    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000017a0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  000019c0    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001bb4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001d90    000001b0     Task.o (.text.Task_Start)
                  00001f40    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000020d2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000020d4    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  0000225c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000023e4    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00002554    0000016c     Interrupt.o (.text.GROUP1_IRQHandler)
                  000026c0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000027fc    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002930    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002a64    00000120            : _printfi.c.obj (.text._pconv_e)
                  00002b84    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002c90    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002d98    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002e7c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002f58    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003030    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00003108    000000cc     Motor.o (.text.Motor_Start)
                  000031d4    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00003298    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  0000335c    000000b8     Motor.o (.text.Motor_SetDirc)
                  00003414    000000b8     Motor.o (.text.Motor_SetDuty)
                  000034cc    000000b4     Task.o (.text.Task_Add)
                  00003580    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003630    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000036d2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000036d4    0000008c                            : mulsf3.S.obj (.text.__mulsf3)
                  00003760    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  000037ec    00000084     Serial.o (.text.MyPrintf)
                  00003870    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000038f4    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003978    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000039fa    00000002     --HOLE-- [fill = 0]
                  000039fc    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00003a7c    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00003afc    00000080     Task_App.o (.text.Task_Init)
                  00003b7c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003bf8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003c6c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003c70    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003ce4    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003d56    00000002     --HOLE-- [fill = 0]
                  00003d58    00000070     Serial.o (.text.MyPrintf_DMA)
                  00003dc8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003e30    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003e96    00000002     --HOLE-- [fill = 0]
                  00003e98    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003efc    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003f60    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00003fc4    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00004026    00000002     --HOLE-- [fill = 0]
                  00004028    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000408a    00000002     --HOLE-- [fill = 0]
                  0000408c    00000060     Task_App.o (.text.Task_IdleFunction)
                  000040ec    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000414a    00000002     --HOLE-- [fill = 0]
                  0000414c    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000041a8    0000005c     Task_App.o (.text.Task_Serial_Test)
                  00004204    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00004260    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000042b8    00000058     Serial.o (.text.Serial_Init)
                  00004310    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004368    00000058            : _printfi.c.obj (.text._pconv_f)
                  000043c0    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004416    00000002     --HOLE-- [fill = 0]
                  00004418    00000054     Interrupt.o (.text.Interrupt_Init)
                  0000446c    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  000044c0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004512    00000002     --HOLE-- [fill = 0]
                  00004514    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00004564    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000045b4    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00004600    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000464c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004698    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000046e4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000472e    00000002     --HOLE-- [fill = 0]
                  00004730    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000477a    00000002     --HOLE-- [fill = 0]
                  0000477c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000047c4    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004808    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  0000484c    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00004890    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000048d2    00000002     --HOLE-- [fill = 0]
                  000048d4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004914    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004954    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004994    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000049d4    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004a14    0000003e     Task.o (.text.Task_CMP)
                  00004a52    00000002     --HOLE-- [fill = 0]
                  00004a54    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004a90    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004acc    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004b08    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004b44    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004b80    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004bbc    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004bf6    00000002     --HOLE-- [fill = 0]
                  00004bf8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004c32    00000002     --HOLE-- [fill = 0]
                  00004c34    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00004c6c    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004ca0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004cd4    00000034     main.o (.text.main)
                  00004d08    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004d38    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00004d68    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00004d98    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004dc8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00004df8    00000030            : vsnprintf.c.obj (.text._outs)
                  00004e28    0000002c     Task_App.o (.text.Task_Encoder_Debug)
                  00004e54    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00004e80    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004eac    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00004ed6    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00004efe    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00004f26    00000002     --HOLE-- [fill = 0]
                  00004f28    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00004f50    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00004f78    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00004fa0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004fc8    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00004ff0    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00005018    00000028     SysTick.o (.text.SysTick_Increasment)
                  00005040    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005068    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  0000508e    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000050b4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000050da    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005100    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005124    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005148    00000024                            : muldi3.S.obj (.text.__muldi3)
                  0000516c    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000518e    00000002     --HOLE-- [fill = 0]
                  00005190    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000051b0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000051d0    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  000051f0    00000020     SysTick.o (.text.Delay)
                  00005210    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000522e    00000002     --HOLE-- [fill = 0]
                  00005230    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000524e    00000002     --HOLE-- [fill = 0]
                  00005250    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  0000526c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005288    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000052a4    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000052c0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000052dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000052f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005314    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00005330    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  0000534c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005368    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005384    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000053a0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000053bc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000053d8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000053f4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  0000540c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005424    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  0000543c    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00005454    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000546c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005484    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000549c    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000054b4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000054cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000054e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000054fc    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00005514    00000018     Motor.o (.text.DL_GPIO_setPins)
                  0000552c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005544    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000555c    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00005574    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000558c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000055a4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000055bc    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000055d4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000055ec    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00005604    00000018     MPU6050.o (.text.DL_I2C_reset)
                  0000561c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005634    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000564c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005664    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  0000567c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005694    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000056ac    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000056c4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000056dc    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000056f4    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  0000570c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005724    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  0000573c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005754    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000576c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005784    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  0000579c    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000057b4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000057ca    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000057e0    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000057f6    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  0000580c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005822    00000016     SysTick.o (.text.SysGetTick)
                  00005838    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000584e    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005862    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005876    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000588a    00000002     --HOLE-- [fill = 0]
                  0000588c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000058a0    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000058b4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000058c8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000058dc    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000058f0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005904    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005918    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000592c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000593e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005950    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005962    00000002     --HOLE-- [fill = 0]
                  00005964    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005974    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005984    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005994    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000059a4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000059b2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000059c0    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000059ce    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000059da    00000002     --HOLE-- [fill = 0]
                  000059dc    0000000c     SysTick.o (.text.Sys_GetTick)
                  000059e8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000059f2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000059fc    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005a0c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005a16    00000002     --HOLE-- [fill = 0]
                  00005a18    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005a28    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005a32    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005a3c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005a46    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005a50    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00005a60    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005a68    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005a70    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005a78    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005a80    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005a86    00000002     --HOLE-- [fill = 0]
                  00005a88    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005a98    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005a9e    00000006            : exit.c.obj (.text:abort)
                  00005aa4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005aa8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005aac    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005ab0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005ac0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005ac4    0000000c     --HOLE-- [fill = 0]

.cinit     0    00005dd0    00000078     
                  00005dd0    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00005e1e    00000002     --HOLE-- [fill = 0]
                  00005e20    0000000c     (__TI_handler_table)
                  00005e2c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005e34    00000010     (__TI_cinit_table)
                  00005e44    00000004     --HOLE-- [fill = 0]

.rodata    0    00005ad0    00000300     
                  00005ad0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005bd1    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00005bd4    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00005bd7    00000001     --HOLE-- [fill = 0]
                  00005bd8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00005c18    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005c40    00000028     inv_mpu.o (.rodata.test)
                  00005c68    00000022     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00005c8a    0000001e     inv_mpu.o (.rodata.reg)
                  00005ca8    0000001d     Task_App.o (.rodata.str1.10836333124219633692.1)
                  00005cc5    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00005cc7    00000001     --HOLE-- [fill = 0]
                  00005cc8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00005ce0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00005cf8    00000018     Task_App.o (.rodata.str1.8463153030208135045.1)
                  00005d10    00000017     Task_App.o (.rodata.str1.11579078728849559700.1)
                  00005d27    00000015     main.o (.rodata.str1.15159059442110792349.1)
                  00005d3c    00000013     main.o (.rodata.str1.8154729771448623357.1)
                  00005d4f    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00005d60    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005d71    00000010     Task_App.o (.rodata.str1.7946239225661045399.1)
                  00005d81    0000000d     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005d8e    0000000c     inv_mpu.o (.rodata.hw)
                  00005d9a    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005da5    00000001     --HOLE-- [fill = 0]
                  00005da6    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00005db0    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005db8    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00005dc0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00005dc8    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005dca    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00005dcc    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000169     UNINITIALIZED
                  202003d4    00000044     Motor.o (.data.Motor_Back_Left)
                  20200418    00000044     Motor.o (.data.Motor_Back_Right)
                  2020045c    00000044     Motor.o (.data.Motor_Font_Left)
                  202004a0    00000044     Motor.o (.data.Motor_Font_Right)
                  202004e4    0000002c     inv_mpu.o (.data.st)
                  20200510    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200520    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200528    00000004     Task_App.o (.data.Task_Serial_Test.test_cnt)
                  2020052c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200530    00000004     SysTick.o (.data.delayTick)
                  20200534    00000004     SysTick.o (.data.uwTick)
                  20200538    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  2020053a    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  2020053b    00000001     Task.o (.data.Task_Num)
                  2020053c    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3674    134       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           52      40        0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3734    366       0      
                                                                 
    .\APP\Src\
       Task_App.o                       360     150       190    
       Interrupt.o                      626     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           986     150       196    
                                                                 
    .\BSP\Src\
       MPU6050.o                        1872    0         47     
       Serial.o                         536     0         512    
       Task.o                           674     0         241    
       Motor.o                          640     0         272    
       No_Mcu_Ganv_Grayscale_Sensor.o   506     0         0      
       SysTick.o                        106     0         8      
       PID_IQMath.o                     110     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4444    0         1080   
                                                                 
    .\DMP\
       inv_mpu.o                        820     82        44     
       inv_mpu_dmp_motion_driver.o      640     0         16     
    +--+--------------------------------+-------+---------+---------+
       Total:                           1460    82        60     
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       292     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        122     0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1144    0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8246    355       4      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2980    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     22998   1067      1852   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005e34 records: 2, size/record: 8, table size: 16
	.data: load addr=00005dd0, load size=0000004e bytes, run addr=202003d4, run size=00000169 bytes, compression=lzss
	.bss: load addr=00005e2c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005e20 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001f41     000059fc     000059fa   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00002d99     00005a18     00005a14   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005a30          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005a44          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005a66          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00005a9c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002b85     00005a50     00005a4e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001f4b     00005a88     00005a84   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005aaa          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005041     00005ab0     00005aac   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003c6d  ADC0_IRQHandler                      
00003c6d  ADC1_IRQHandler                      
00003c6d  AES_IRQHandler                       
00005aa4  C$$EXIT                              
00003c6d  CANFD0_IRQHandler                    
00003c6d  DAC0_IRQHandler                      
000048d5  DL_ADC12_setClockConfig              
000059e9  DL_Common_delayCycles                
00004601  DL_DMA_initChannel                   
000040ed  DL_I2C_fillControllerTXFIFO          
00004acd  DL_I2C_flushControllerTXFIFO         
000050db  DL_I2C_setClockConfig                
00002e7d  DL_SYSCTL_configSYSPLL               
00003e99  DL_SYSCTL_setHFCLKSourceHFXTParams   
000047c5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000031d5  DL_Timer_initPWMMode                 
000053a1  DL_Timer_setCaptCompUpdateMethod     
000056c5  DL_Timer_setCaptureCompareOutCtl     
00005975  DL_Timer_setCaptureCompareValue      
000053bd  DL_Timer_setClockConfig              
0000477d  DL_UART_init                         
0000592d  DL_UART_setClockConfig               
000051d1  DL_UART_transmitDataBlocking         
00003c6d  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
20200520  Data_MotorEncoder                    
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
00003c6d  Default_Handler                      
000051f1  Delay                                
202003c8  ExISR_Flag                           
2020053a  Flag_MPU6050_Ready                   
00003c6d  GROUP0_IRQHandler                    
00002555  GROUP1_IRQHandler                    
202002f0  GraySensor                           
00005aa5  HOSTexit                             
00003c6d  HardFault_Handler                    
00003c6d  I2C0_IRQHandler                      
00003c6d  I2C1_IRQHandler                      
00004419  Interrupt_Init                       
202003d4  Motor_Back_Left                      
20200418  Motor_Back_Right                     
2020045c  Motor_Font_Left                      
202004a0  Motor_Font_Right                     
00003415  Motor_SetDuty                        
00003109  Motor_Start                          
000037ed  MyPrintf                             
00003d59  MyPrintf_DMA                         
00003c6d  NMI_Handler                          
000020d5  No_MCU_Ganv_Sensor_Init              
00003ce5  No_MCU_Ganv_Sensor_Init_Frist        
00004ead  PID_IQ_Init                          
00004809  PID_IQ_SetParams                     
00003c6d  PendSV_Handler                       
00003c6d  RTC_IRQHandler                       
00001349  Read_Quad                            
00005aad  Reset_Handler                        
00003c6d  SPI0_IRQHandler                      
00003c6d  SPI1_IRQHandler                      
00003c6d  SVC_Handler                          
00004699  SYSCFG_DL_ADC1_init                  
00004d69  SYSCFG_DL_DMA_CH_RX_init             
00005785  SYSCFG_DL_DMA_CH_TX_init             
000059cf  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
00004261  SYSCFG_DL_I2C_MPU6050_init           
00003efd  SYSCFG_DL_I2C_OLED_init              
000039fd  SYSCFG_DL_MotorBack_init             
00003a7d  SYSCFG_DL_MotorFront_init            
0000414d  SYSCFG_DL_SYSCTL_init                
00005985  SYSCFG_DL_SYSTICK_init               
00003871  SYSCFG_DL_UART0_init                 
00004d99  SYSCFG_DL_init                       
00003581  SYSCFG_DL_initPower                  
000042b9  Serial_Init                          
20200000  Serial_RxData                        
00005823  SysGetTick                           
00005a69  SysTick_Handler                      
00005019  SysTick_Increasment                  
000059dd  Sys_GetTick                          
00003c6d  TIMA0_IRQHandler                     
00003c6d  TIMA1_IRQHandler                     
00003c6d  TIMG0_IRQHandler                     
00003c6d  TIMG12_IRQHandler                    
00003c6d  TIMG6_IRQHandler                     
00003c6d  TIMG7_IRQHandler                     
00003c6d  TIMG8_IRQHandler                     
0000593f  TI_memcpy_small                      
000059c1  TI_memset_small                      
000034cd  Task_Add                             
00004e29  Task_Encoder_Debug                   
0000408d  Task_IdleFunction                    
00003afd  Task_Init                            
000041a9  Task_Serial_Test                     
00001d91  Task_Start                           
00003c6d  UART0_IRQHandler                     
00003c6d  UART1_IRQHandler                     
00003c6d  UART2_IRQHandler                     
00003c6d  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005e34  __TI_CINIT_Base                      
00005e44  __TI_CINIT_Limit                     
00005e44  __TI_CINIT_Warm                      
00005e20  __TI_Handler_Table_Base              
00005e2c  __TI_Handler_Table_Limit             
00004b81  __TI_auto_init_nobinit_nopinit       
00003b7d  __TI_decompress_lzss                 
00005951  __TI_decompress_none                 
00004311  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005839  __TI_zero_init_nomemset              
00001f4b  __adddf3                             
0000303b  __addsf3                             
00005ad0  __aeabi_ctype_table_                 
00005ad0  __aeabi_ctype_table_C                
00003c71  __aeabi_d2f                          
00004731  __aeabi_d2iz                         
00004891  __aeabi_d2uiz                        
00001f4b  __aeabi_dadd                         
00003fc5  __aeabi_dcmpeq                       
00004001  __aeabi_dcmpge                       
00004015  __aeabi_dcmpgt                       
00003fed  __aeabi_dcmple                       
00003fd9  __aeabi_dcmplt                       
00002b85  __aeabi_ddiv                         
00002d99  __aeabi_dmul                         
00001f41  __aeabi_dsub                         
2020052c  __aeabi_errno                        
00005a71  __aeabi_errno_addr                   
00004955  __aeabi_f2d                          
00004c35  __aeabi_f2iz                         
0000303b  __aeabi_fadd                         
00004029  __aeabi_fcmpeq                       
00004065  __aeabi_fcmpge                       
00004079  __aeabi_fcmpgt                       
00004051  __aeabi_fcmple                       
0000403d  __aeabi_fcmplt                       
00003979  __aeabi_fdiv                         
000036d5  __aeabi_fmul                         
00003031  __aeabi_fsub                         
00004e81  __aeabi_i2d                          
00004b09  __aeabi_i2f                          
000043c1  __aeabi_idiv                         
000020d3  __aeabi_idiv0                        
000043c1  __aeabi_idivmod                      
000036d3  __aeabi_ldiv0                        
00005231  __aeabi_llsl                         
00005149  __aeabi_lmul                         
00005a79  __aeabi_memcpy                       
00005a79  __aeabi_memcpy4                      
00005a79  __aeabi_memcpy8                      
000059a5  __aeabi_memset                       
000059a5  __aeabi_memset4                      
000059a5  __aeabi_memset8                      
00005125  __aeabi_ui2d                         
00004915  __aeabi_uidiv                        
00004915  __aeabi_uidivmod                     
00005905  __aeabi_uldivmod                     
00005231  __ashldi3                            
ffffffff  __binit__                            
00003dc9  __cmpdf2                             
00004bbd  __cmpsf2                             
00002b85  __divdf3                             
00003979  __divsf3                             
00003dc9  __eqdf2                              
00004bbd  __eqsf2                              
00004955  __extendsfdf2                        
00004731  __fixdfsi                            
00004c35  __fixsfsi                            
00004891  __fixunsdfsi                         
00004e81  __floatsidf                          
00004b09  __floatsisf                          
00005125  __floatunsidf                        
00003bf9  __gedf2                              
00004b45  __gesf2                              
00003bf9  __gtdf2                              
00004b45  __gtsf2                              
00003dc9  __ledf2                              
00004bbd  __lesf2                              
00003dc9  __ltdf2                              
00004bbd  __ltsf2                              
UNDEFED   __mpu_init                           
00002d99  __muldf3                             
00005149  __muldi3                             
00004bf9  __muldsi3                            
000036d5  __mulsf3                             
00003dc9  __nedf2                              
00004bbd  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001f41  __subdf3                             
00003031  __subsf3                             
00003c71  __truncdfsf2                         
00003631  __udivmoddi4                         
00005041  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00005ac1  _system_pre_init                     
00005a9f  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000225d  atan2                                
0000225d  atan2l                               
00000df5  atanl                                
00004995  atoi                                 
ffffffff  binit                                
20200530  delayTick                            
000019c1  dmp_read_fifo                        
2020053c  enable_group1_irq                    
00004205  frexp                                
00004205  frexpl                               
00005d8e  hw                                   
00000000  interruptVectors                     
00002f59  ldexp                                
00002f59  ldexpl                               
00004cd5  main                                 
0000516d  memccpy                              
202003d2  more                                 
00003f61  mpu6050_i2c_sda_unlock               
00002c91  mpu_read_fifo_stream                 
00001575  mpu_reset_fifo                       
000027fd  mspm0_i2c_read                       
00003299  mspm0_i2c_write                      
00002931  qsort                                
202003a0  quat                                 
00005c8a  reg                                  
00002f59  scalbn                               
00002f59  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
000023e5  sqrt                                 
000023e5  sqrtl                                
00005c40  test                                 
20200534  uwTick                               
000049d5  vsnprintf                            
00005995  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
00001349  Read_Quad                            
00001575  mpu_reset_fifo                       
000019c1  dmp_read_fifo                        
00001d91  Task_Start                           
00001f41  __aeabi_dsub                         
00001f41  __subdf3                             
00001f4b  __adddf3                             
00001f4b  __aeabi_dadd                         
000020d3  __aeabi_idiv0                        
000020d5  No_MCU_Ganv_Sensor_Init              
0000225d  atan2                                
0000225d  atan2l                               
000023e5  sqrt                                 
000023e5  sqrtl                                
00002555  GROUP1_IRQHandler                    
000027fd  mspm0_i2c_read                       
00002931  qsort                                
00002b85  __aeabi_ddiv                         
00002b85  __divdf3                             
00002c91  mpu_read_fifo_stream                 
00002d99  __aeabi_dmul                         
00002d99  __muldf3                             
00002e7d  DL_SYSCTL_configSYSPLL               
00002f59  ldexp                                
00002f59  ldexpl                               
00002f59  scalbn                               
00002f59  scalbnl                              
00003031  __aeabi_fsub                         
00003031  __subsf3                             
0000303b  __addsf3                             
0000303b  __aeabi_fadd                         
00003109  Motor_Start                          
000031d5  DL_Timer_initPWMMode                 
00003299  mspm0_i2c_write                      
00003415  Motor_SetDuty                        
000034cd  Task_Add                             
00003581  SYSCFG_DL_initPower                  
00003631  __udivmoddi4                         
000036d3  __aeabi_ldiv0                        
000036d5  __aeabi_fmul                         
000036d5  __mulsf3                             
000037ed  MyPrintf                             
00003871  SYSCFG_DL_UART0_init                 
00003979  __aeabi_fdiv                         
00003979  __divsf3                             
000039fd  SYSCFG_DL_MotorBack_init             
00003a7d  SYSCFG_DL_MotorFront_init            
00003afd  Task_Init                            
00003b7d  __TI_decompress_lzss                 
00003bf9  __gedf2                              
00003bf9  __gtdf2                              
00003c6d  ADC0_IRQHandler                      
00003c6d  ADC1_IRQHandler                      
00003c6d  AES_IRQHandler                       
00003c6d  CANFD0_IRQHandler                    
00003c6d  DAC0_IRQHandler                      
00003c6d  DMA_IRQHandler                       
00003c6d  Default_Handler                      
00003c6d  GROUP0_IRQHandler                    
00003c6d  HardFault_Handler                    
00003c6d  I2C0_IRQHandler                      
00003c6d  I2C1_IRQHandler                      
00003c6d  NMI_Handler                          
00003c6d  PendSV_Handler                       
00003c6d  RTC_IRQHandler                       
00003c6d  SPI0_IRQHandler                      
00003c6d  SPI1_IRQHandler                      
00003c6d  SVC_Handler                          
00003c6d  TIMA0_IRQHandler                     
00003c6d  TIMA1_IRQHandler                     
00003c6d  TIMG0_IRQHandler                     
00003c6d  TIMG12_IRQHandler                    
00003c6d  TIMG6_IRQHandler                     
00003c6d  TIMG7_IRQHandler                     
00003c6d  TIMG8_IRQHandler                     
00003c6d  UART0_IRQHandler                     
00003c6d  UART1_IRQHandler                     
00003c6d  UART2_IRQHandler                     
00003c6d  UART3_IRQHandler                     
00003c71  __aeabi_d2f                          
00003c71  __truncdfsf2                         
00003ce5  No_MCU_Ganv_Sensor_Init_Frist        
00003d59  MyPrintf_DMA                         
00003dc9  __cmpdf2                             
00003dc9  __eqdf2                              
00003dc9  __ledf2                              
00003dc9  __ltdf2                              
00003dc9  __nedf2                              
00003e99  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003efd  SYSCFG_DL_I2C_OLED_init              
00003f61  mpu6050_i2c_sda_unlock               
00003fc5  __aeabi_dcmpeq                       
00003fd9  __aeabi_dcmplt                       
00003fed  __aeabi_dcmple                       
00004001  __aeabi_dcmpge                       
00004015  __aeabi_dcmpgt                       
00004029  __aeabi_fcmpeq                       
0000403d  __aeabi_fcmplt                       
00004051  __aeabi_fcmple                       
00004065  __aeabi_fcmpge                       
00004079  __aeabi_fcmpgt                       
0000408d  Task_IdleFunction                    
000040ed  DL_I2C_fillControllerTXFIFO          
0000414d  SYSCFG_DL_SYSCTL_init                
000041a9  Task_Serial_Test                     
00004205  frexp                                
00004205  frexpl                               
00004261  SYSCFG_DL_I2C_MPU6050_init           
000042b9  Serial_Init                          
00004311  __TI_ltoa                            
000043c1  __aeabi_idiv                         
000043c1  __aeabi_idivmod                      
00004419  Interrupt_Init                       
00004601  DL_DMA_initChannel                   
00004699  SYSCFG_DL_ADC1_init                  
00004731  __aeabi_d2iz                         
00004731  __fixdfsi                            
0000477d  DL_UART_init                         
000047c5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004809  PID_IQ_SetParams                     
00004891  __aeabi_d2uiz                        
00004891  __fixunsdfsi                         
000048d5  DL_ADC12_setClockConfig              
00004915  __aeabi_uidiv                        
00004915  __aeabi_uidivmod                     
00004955  __aeabi_f2d                          
00004955  __extendsfdf2                        
00004995  atoi                                 
000049d5  vsnprintf                            
00004acd  DL_I2C_flushControllerTXFIFO         
00004b09  __aeabi_i2f                          
00004b09  __floatsisf                          
00004b45  __gesf2                              
00004b45  __gtsf2                              
00004b81  __TI_auto_init_nobinit_nopinit       
00004bbd  __cmpsf2                             
00004bbd  __eqsf2                              
00004bbd  __lesf2                              
00004bbd  __ltsf2                              
00004bbd  __nesf2                              
00004bf9  __muldsi3                            
00004c35  __aeabi_f2iz                         
00004c35  __fixsfsi                            
00004cd5  main                                 
00004d69  SYSCFG_DL_DMA_CH_RX_init             
00004d99  SYSCFG_DL_init                       
00004e29  Task_Encoder_Debug                   
00004e81  __aeabi_i2d                          
00004e81  __floatsidf                          
00004ead  PID_IQ_Init                          
00005019  SysTick_Increasment                  
00005041  _c_int00_noargs                      
000050db  DL_I2C_setClockConfig                
00005125  __aeabi_ui2d                         
00005125  __floatunsidf                        
00005149  __aeabi_lmul                         
00005149  __muldi3                             
0000516d  memccpy                              
000051d1  DL_UART_transmitDataBlocking         
000051f1  Delay                                
00005231  __aeabi_llsl                         
00005231  __ashldi3                            
000053a1  DL_Timer_setCaptCompUpdateMethod     
000053bd  DL_Timer_setClockConfig              
000056c5  DL_Timer_setCaptureCompareOutCtl     
00005785  SYSCFG_DL_DMA_CH_TX_init             
00005823  SysGetTick                           
00005839  __TI_zero_init_nomemset              
00005905  __aeabi_uldivmod                     
0000592d  DL_UART_setClockConfig               
0000593f  TI_memcpy_small                      
00005951  __TI_decompress_none                 
00005975  DL_Timer_setCaptureCompareValue      
00005985  SYSCFG_DL_SYSTICK_init               
00005995  wcslen                               
000059a5  __aeabi_memset                       
000059a5  __aeabi_memset4                      
000059a5  __aeabi_memset8                      
000059c1  TI_memset_small                      
000059cf  SYSCFG_DL_DMA_init                   
000059dd  Sys_GetTick                          
000059e9  DL_Common_delayCycles                
00005a69  SysTick_Handler                      
00005a71  __aeabi_errno_addr                   
00005a79  __aeabi_memcpy                       
00005a79  __aeabi_memcpy4                      
00005a79  __aeabi_memcpy8                      
00005a9f  abort                                
00005aa4  C$$EXIT                              
00005aa5  HOSTexit                             
00005aad  Reset_Handler                        
00005ac1  _system_pre_init                     
00005ad0  __aeabi_ctype_table_                 
00005ad0  __aeabi_ctype_table_C                
00005c40  test                                 
00005c8a  reg                                  
00005d8e  hw                                   
00005e20  __TI_Handler_Table_Base              
00005e2c  __TI_Handler_Table_Limit             
00005e34  __TI_CINIT_Base                      
00005e44  __TI_CINIT_Limit                     
00005e44  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Back_Left                      
20200418  Motor_Back_Right                     
2020045c  Motor_Font_Left                      
202004a0  Motor_Font_Right                     
20200520  Data_MotorEncoder                    
2020052c  __aeabi_errno                        
20200530  delayTick                            
20200534  uwTick                               
2020053a  Flag_MPU6050_Ready                   
2020053c  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[255 symbols]
