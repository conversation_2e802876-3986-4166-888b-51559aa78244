******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 17:55:06 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000053b9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006208  00019df8  R  X
  SRAM                  20200000   00008000  00000767  00007899  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006208   00006208    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005e60   00005e60    r-x .text
  00005f20    00005f20    00000270   00000270    r-- .rodata
  00006190    00006190    00000078   00000078    r-- .cinit
20200000    20200000    00000568   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000194   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005e60     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    0000025c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001348    0000022c     MPU6050.o (.text.Read_Quad)
                  00001574    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000017a0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  000019c0    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001bb4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001d90    000001b0     Task.o (.text.Task_Start)
                  00001f40    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000020d2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000020d4    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  0000225c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000023e4    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00002554    0000016c     Interrupt.o (.text.GROUP1_IRQHandler)
                  000026c0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000027fc    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002930    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002a64    00000128     Task_App.o (.text.Task_Tracker)
                  00002b8c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002cac    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002db8    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002ec0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002fa4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003080    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000315c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003234    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000330c    000000cc     Motor.o (.text.Motor_Start)
                  000033d8    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  0000349c    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00003560    000000b8     Motor.o (.text.Motor_SetDirc)
                  00003618    000000b8     Motor.o (.text.Motor_SetDuty)
                  000036d0    000000b4     Task.o (.text.Task_Add)
                  00003784    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003834    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000038de    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000038e0    000000a2                            : udivmoddi4.S.obj (.text)
                  00003982    00000002     --HOLE-- [fill = 0]
                  00003984    0000008c                            : mulsf3.S.obj (.text.__mulsf3)
                  00003a10    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00003a9c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00003b20    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003ba4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003c26    00000002     --HOLE-- [fill = 0]
                  00003c28    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00003ca8    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00003d28    0000007c     Task_App.o (.text.Task_Init)
                  00003da4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003e20    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003e94    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00003ea0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003f14    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00003f86    00000002     --HOLE-- [fill = 0]
                  00003f88    00000070     Serial.o (.text.MyPrintf_DMA)
                  00003ff8    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00004064    00000068     Task_App.o (.text.Task_GraySensor)
                  000040cc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00004134    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000419a    00000002     --HOLE-- [fill = 0]
                  0000419c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00004200    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00004264    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000042c8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000432a    00000002     --HOLE-- [fill = 0]
                  0000432c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000438e    00000002     --HOLE-- [fill = 0]
                  00004390    00000060     Task_App.o (.text.Task_IdleFunction)
                  000043f0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000444e    00000002     --HOLE-- [fill = 0]
                  00004450    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000044ac    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00004508    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00004560    00000058     Serial.o (.text.Serial_Init)
                  000045b8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00004610    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004668    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000046be    00000002     --HOLE-- [fill = 0]
                  000046c0    00000054     Interrupt.o (.text.Interrupt_Init)
                  00004714    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  00004768    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000047ba    00000002     --HOLE-- [fill = 0]
                  000047bc    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000480c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000485c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000048a8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000048f4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004940    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  0000498c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000049d6    00000002     --HOLE-- [fill = 0]
                  000049d8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004a22    00000002     --HOLE-- [fill = 0]
                  00004a24    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004a6c    00000048     ADC.o (.text.adc_getValue)
                  00004ab4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004af8    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00004b3c    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00004b80    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00004bc2    00000002     --HOLE-- [fill = 0]
                  00004bc4    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00004c06    00000002     --HOLE-- [fill = 0]
                  00004c08    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004c48    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004c88    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004cc8    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004d08    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004d48    0000003e     Task.o (.text.Task_CMP)
                  00004d86    00000002     --HOLE-- [fill = 0]
                  00004d88    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004dc4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004e00    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004e3c    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00004e78    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004eb4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004ef0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004f2c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004f66    00000002     --HOLE-- [fill = 0]
                  00004f68    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004fa2    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00004fda    00000002     --HOLE-- [fill = 0]
                  00004fdc    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00005014    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005048    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000507c    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  000050ac    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000050dc    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  0000510c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  0000513c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000516c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  0000519c    00000030            : vsnprintf.c.obj (.text._outs)
                  000051cc    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000051f8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00005224    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000524e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00005276    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000529e    00000002     --HOLE-- [fill = 0]
                  000052a0    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000052c8    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000052f0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00005318    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00005340    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005368    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00005390    00000028     SysTick.o (.text.SysTick_Increasment)
                  000053b8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000053e0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00005406    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000542c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005452    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005478    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000549c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000054c0    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000054e4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005506    00000002     --HOLE-- [fill = 0]
                  00005508    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005528    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005548    00000020     SysTick.o (.text.Delay)
                  00005568    00000020     main.o (.text.main)
                  00005588    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000055a6    00000002     --HOLE-- [fill = 0]
                  000055a8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000055c6    00000002     --HOLE-- [fill = 0]
                  000055c8    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000055e4    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00005600    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  0000561c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005638    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005654    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00005670    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000568c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000056a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000056c4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000056e0    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000056fc    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005718    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005734    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005750    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000576c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005788    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000057a4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000057bc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000057d4    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000057ec    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00005804    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000581c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005834    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000584c    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00005864    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000587c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00005894    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000058ac    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000058c4    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000058dc    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000058f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000590c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005924    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  0000593c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005954    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000596c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005984    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  0000599c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000059b4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000059cc    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000059e4    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000059fc    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005a14    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005a2c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00005a44    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005a5c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005a74    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005a8c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005aa4    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00005abc    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00005ad4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005aec    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005b04    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005b1c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005b34    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005b4c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005b64    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00005b7c    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00005b94    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00005bac    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00005bc2    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00005bd8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005bee    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00005c04    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00005c1a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00005c30    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005c46    00000016     SysTick.o (.text.SysGetTick)
                  00005c5c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005c72    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005c86    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005c9a    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00005cae    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005cc2    00000002     --HOLE-- [fill = 0]
                  00005cc4    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00005cd8    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00005cec    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005d00    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005d14    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005d28    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005d3c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005d50    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005d64    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005d76    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005d88    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005d9a    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00005daa    00000002     --HOLE-- [fill = 0]
                  00005dac    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005dbc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005dcc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005ddc    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005dec    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00005dfa    00000002     --HOLE-- [fill = 0]
                  00005dfc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005e0a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005e18    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005e26    00000002     --HOLE-- [fill = 0]
                  00005e28    0000000c     SysTick.o (.text.Sys_GetTick)
                  00005e34    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005e3e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005e48    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005e58    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005e62    00000002     --HOLE-- [fill = 0]
                  00005e64    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005e74    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005e7e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005e88    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005e92    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005e9c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00005eac    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005eb4    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005ebc    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005ec4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005ecc    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005ed2    00000002     --HOLE-- [fill = 0]
                  00005ed4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005ee4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005eea    00000006            : exit.c.obj (.text:abort)
                  00005ef0    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005ef4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005ef8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005efc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005f00    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005f10    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005f14    0000000c     --HOLE-- [fill = 0]

.cinit     0    00006190    00000078     
                  00006190    00000050     (.cinit..data.load) [load image, compression = lzss]
                  000061e0    0000000c     (__TI_handler_table)
                  000061ec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000061f4    00000010     (__TI_cinit_table)
                  00006204    00000004     --HOLE-- [fill = 0]

.rodata    0    00005f20    00000270     
                  00005f20    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006021    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00006024    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00006027    00000001     --HOLE-- [fill = 0]
                  00006028    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00006068    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006090    00000028     inv_mpu.o (.rodata.test)
                  000060b8    0000001e     inv_mpu.o (.rodata.reg)
                  000060d6    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000060d8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000060f0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00006108    00000017     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000611f    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00006130    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00006141    00000001     --HOLE-- [fill = 0]
                  00006142    0000000c     inv_mpu.o (.rodata.hw)
                  0000614e    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00006159    00000001     --HOLE-- [fill = 0]
                  0000615a    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00006164    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000616c    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00006174    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  0000617c    00000008     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00006184    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006186    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00006188    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000194     UNINITIALIZED
                  202003d4    00000044     Motor.o (.data.Motor_Back_Left)
                  20200418    00000044     Motor.o (.data.Motor_Back_Right)
                  2020045c    00000044     Motor.o (.data.Motor_Font_Left)
                  202004a0    00000044     Motor.o (.data.Motor_Font_Right)
                  202004e4    0000002c     inv_mpu.o (.data.st)
                  20200510    00000010     Task_App.o (.data.Gray_Anolog)
                  20200520    00000010     Task_App.o (.data.Gray_Normal)
                  20200530    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200540    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200548    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200550    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200554    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200558    00000004     SysTick.o (.data.delayTick)
                  2020055c    00000004     SysTick.o (.data.uwTick)
                  20200560    00000002     Task_App.o (.data.Task_GraySensor.debug_cnt)
                  20200562    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200564    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200565    00000001     Task_App.o (.data.Gray_Digtal)
                  20200566    00000001     Task.o (.data.Task_Num)
                  20200567    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3674    134       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3714    326       0      
                                                                 
    .\APP\Src\
       Task_App.o                       620     42        233    
       Interrupt.o                      626     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1246    42        239    
                                                                 
    .\BSP\Src\
       MPU6050.o                        1872    0         47     
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          640     0         272    
       ADC.o                            236     0         0      
       SysTick.o                        106     0         8      
       PID_IQMath.o                     110     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5286    0         1080   
                                                                 
    .\DMP\
       inv_mpu.o                        820     82        44     
       inv_mpu_dmp_motion_driver.o      640     0         16     
    +--+--------------------------------+-------+---------+---------+
       Total:                           1460    82        60     
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       292     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1112    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           48      0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8246    355       4      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2980    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     24096   921       1895   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000061f4 records: 2, size/record: 8, table size: 16
	.data: load addr=00006190, load size=00000050 bytes, run addr=202003d4, run size=00000194 bytes, compression=lzss
	.bss: load addr=000061ec, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000061e0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001f41     00005e48     00005e46   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00002ec1     00005e64     00005e60   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005e7c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005e90          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005eb2          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00005ee8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002cad     00005e9c     00005e9a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001f4b     00005ed4     00005ed0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005efa          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000053b9     00005f00     00005efc   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005ef1  ADC0_IRQHandler                      
00005ef1  ADC1_IRQHandler                      
00005ef1  AES_IRQHandler                       
00005ef4  C$$EXIT                              
00005ef1  CANFD0_IRQHandler                    
00005ef1  DAC0_IRQHandler                      
00004c09  DL_ADC12_setClockConfig              
00005e35  DL_Common_delayCycles                
000048a9  DL_DMA_initChannel                   
000043f1  DL_I2C_fillControllerTXFIFO          
00004e01  DL_I2C_flushControllerTXFIFO         
00005453  DL_I2C_setClockConfig                
00002fa5  DL_SYSCTL_configSYSPLL               
0000419d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004ab5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000033d9  DL_Timer_initPWMMode                 
00005751  DL_Timer_setCaptCompUpdateMethod     
00005a8d  DL_Timer_setCaptureCompareOutCtl     
00005dbd  DL_Timer_setCaptureCompareValue      
0000576d  DL_Timer_setClockConfig              
00004a25  DL_UART_init                         
00005d65  DL_UART_setClockConfig               
00005ef1  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
20200540  Data_MotorEncoder                    
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
20200548  Data_Tracker_Input                   
20200550  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00005ef1  Default_Handler                      
00005549  Delay                                
202003c8  ExISR_Flag                           
20200564  Flag_MPU6050_Ready                   
00005ef1  GROUP0_IRQHandler                    
00002555  GROUP1_IRQHandler                    
00003081  Get_Analog_value                     
00004e3d  Get_Anolog_Value                     
00005ded  Get_Digtal_For_User                  
00004fa3  Get_Normalize_For_User               
202002f0  GraySensor                           
20200510  Gray_Anolog                          
20200565  Gray_Digtal                          
20200520  Gray_Normal                          
00005ef5  HOSTexit                             
00005ef1  HardFault_Handler                    
00005ef1  I2C0_IRQHandler                      
00005ef1  I2C1_IRQHandler                      
000046c1  Interrupt_Init                       
202003d4  Motor_Back_Left                      
20200418  Motor_Back_Right                     
2020045c  Motor_Font_Left                      
202004a0  Motor_Font_Right                     
00003619  Motor_SetDuty                        
0000330d  Motor_Start                          
00003f89  MyPrintf_DMA                         
00005ef1  NMI_Handler                          
000020d5  No_MCU_Ganv_Sensor_Init              
00003f15  No_MCU_Ganv_Sensor_Init_Frist        
00004b81  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005225  PID_IQ_Init                          
00004af9  PID_IQ_SetParams                     
00005ef1  PendSV_Handler                       
00005ef1  RTC_IRQHandler                       
00001349  Read_Quad                            
00005efd  Reset_Handler                        
00005ef1  SPI0_IRQHandler                      
00005ef1  SPI1_IRQHandler                      
00005ef1  SVC_Handler                          
00004941  SYSCFG_DL_ADC1_init                  
0000510d  SYSCFG_DL_DMA_CH_RX_init             
00005b4d  SYSCFG_DL_DMA_CH_TX_init             
00003e95  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
00004509  SYSCFG_DL_I2C_MPU6050_init           
00004201  SYSCFG_DL_I2C_OLED_init              
00003c29  SYSCFG_DL_MotorBack_init             
00003ca9  SYSCFG_DL_MotorFront_init            
00004451  SYSCFG_DL_SYSCTL_init                
00005dcd  SYSCFG_DL_SYSTICK_init               
00003a9d  SYSCFG_DL_UART0_init                 
0000513d  SYSCFG_DL_init                       
00003785  SYSCFG_DL_initPower                  
00004561  Serial_Init                          
20200000  Serial_RxData                        
00005c47  SysGetTick                           
00005eb5  SysTick_Handler                      
00005391  SysTick_Increasment                  
00005e29  Sys_GetTick                          
00005ef1  TIMA0_IRQHandler                     
00005ef1  TIMA1_IRQHandler                     
00005ef1  TIMG0_IRQHandler                     
00005ef1  TIMG12_IRQHandler                    
00005ef1  TIMG6_IRQHandler                     
00005ef1  TIMG7_IRQHandler                     
00005ef1  TIMG8_IRQHandler                     
00005d77  TI_memcpy_small                      
00005e19  TI_memset_small                      
000036d1  Task_Add                             
00004065  Task_GraySensor                      
00004391  Task_IdleFunction                    
00003d29  Task_Init                            
00001d91  Task_Start                           
00002a65  Task_Tracker                         
00005ef1  UART0_IRQHandler                     
00005ef1  UART1_IRQHandler                     
00005ef1  UART2_IRQHandler                     
00005ef1  UART3_IRQHandler                     
00005b65  _IQ24div                             
00005b7d  _IQ24mpy                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000061f4  __TI_CINIT_Base                      
00006204  __TI_CINIT_Limit                     
00006204  __TI_CINIT_Warm                      
000061e0  __TI_Handler_Table_Base              
000061ec  __TI_Handler_Table_Limit             
00004ef1  __TI_auto_init_nobinit_nopinit       
00003da5  __TI_decompress_lzss                 
00005d89  __TI_decompress_none                 
000045b9  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005c5d  __TI_zero_init_nomemset              
00001f4b  __adddf3                             
0000323f  __addsf3                             
00005f20  __aeabi_ctype_table_                 
00005f20  __aeabi_ctype_table_C                
00003ea1  __aeabi_d2f                          
000049d9  __aeabi_d2iz                         
00004bc5  __aeabi_d2uiz                        
00001f4b  __aeabi_dadd                         
000042c9  __aeabi_dcmpeq                       
00004305  __aeabi_dcmpge                       
00004319  __aeabi_dcmpgt                       
000042f1  __aeabi_dcmple                       
000042dd  __aeabi_dcmplt                       
00002cad  __aeabi_ddiv                         
00002ec1  __aeabi_dmul                         
00001f41  __aeabi_dsub                         
20200554  __aeabi_errno                        
00005ebd  __aeabi_errno_addr                   
00004c89  __aeabi_f2d                          
00004fdd  __aeabi_f2iz                         
0000323f  __aeabi_fadd                         
0000432d  __aeabi_fcmpeq                       
00004369  __aeabi_fcmpge                       
0000437d  __aeabi_fcmpgt                       
00004355  __aeabi_fcmple                       
00004341  __aeabi_fcmplt                       
00003ba5  __aeabi_fdiv                         
00003985  __aeabi_fmul                         
00003235  __aeabi_fsub                         
000051f9  __aeabi_i2d                          
00004e79  __aeabi_i2f                          
00004669  __aeabi_idiv                         
000020d3  __aeabi_idiv0                        
00004669  __aeabi_idivmod                      
000038df  __aeabi_ldiv0                        
000055a9  __aeabi_llsl                         
000054c1  __aeabi_lmul                         
00005ec5  __aeabi_memcpy                       
00005ec5  __aeabi_memcpy4                      
00005ec5  __aeabi_memcpy8                      
00005dfd  __aeabi_memset                       
00005dfd  __aeabi_memset4                      
00005dfd  __aeabi_memset8                      
0000549d  __aeabi_ui2d                         
00004c49  __aeabi_uidiv                        
00004c49  __aeabi_uidivmod                     
00005d3d  __aeabi_uldivmod                     
000055a9  __ashldi3                            
ffffffff  __binit__                            
000040cd  __cmpdf2                             
00004f2d  __cmpsf2                             
00002cad  __divdf3                             
00003ba5  __divsf3                             
000040cd  __eqdf2                              
00004f2d  __eqsf2                              
00004c89  __extendsfdf2                        
000049d9  __fixdfsi                            
00004fdd  __fixsfsi                            
00004bc5  __fixunsdfsi                         
000051f9  __floatsidf                          
00004e79  __floatsisf                          
0000549d  __floatunsidf                        
00003e21  __gedf2                              
00004eb5  __gesf2                              
00003e21  __gtdf2                              
00004eb5  __gtsf2                              
000040cd  __ledf2                              
00004f2d  __lesf2                              
000040cd  __ltdf2                              
00004f2d  __ltsf2                              
UNDEFED   __mpu_init                           
00002ec1  __muldf3                             
000054c1  __muldi3                             
00004f69  __muldsi3                            
00003985  __mulsf3                             
000040cd  __nedf2                              
00004f2d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001f41  __subdf3                             
00003235  __subsf3                             
00003ea1  __truncdfsf2                         
000038e1  __udivmoddi4                         
000053b9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00005f11  _system_pre_init                     
00005eeb  abort                                
00004a6d  adc_getValue                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000225d  atan2                                
0000225d  atan2l                               
00000df5  atanl                                
00004cc9  atoi                                 
ffffffff  binit                                
00003ff9  convertAnalogToDigital               
20200558  delayTick                            
000019c1  dmp_read_fifo                        
20200567  enable_group1_irq                    
000044ad  frexp                                
000044ad  frexpl                               
00006142  hw                                   
00000000  interruptVectors                     
0000315d  ldexp                                
0000315d  ldexpl                               
00005569  main                                 
000054e5  memccpy                              
202003d2  more                                 
00004265  mpu6050_i2c_sda_unlock               
00002db9  mpu_read_fifo_stream                 
00001575  mpu_reset_fifo                       
000027fd  mspm0_i2c_read                       
0000349d  mspm0_i2c_write                      
00003835  normalizeAnalogValues                
00002931  qsort                                
202003a0  quat                                 
000060b8  reg                                  
0000315d  scalbn                               
0000315d  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
000023e5  sqrt                                 
000023e5  sqrtl                                
00006090  test                                 
2020055c  uwTick                               
00004d09  vsnprintf                            
00005ddd  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
00001349  Read_Quad                            
00001575  mpu_reset_fifo                       
000019c1  dmp_read_fifo                        
00001d91  Task_Start                           
00001f41  __aeabi_dsub                         
00001f41  __subdf3                             
00001f4b  __adddf3                             
00001f4b  __aeabi_dadd                         
000020d3  __aeabi_idiv0                        
000020d5  No_MCU_Ganv_Sensor_Init              
0000225d  atan2                                
0000225d  atan2l                               
000023e5  sqrt                                 
000023e5  sqrtl                                
00002555  GROUP1_IRQHandler                    
000027fd  mspm0_i2c_read                       
00002931  qsort                                
00002a65  Task_Tracker                         
00002cad  __aeabi_ddiv                         
00002cad  __divdf3                             
00002db9  mpu_read_fifo_stream                 
00002ec1  __aeabi_dmul                         
00002ec1  __muldf3                             
00002fa5  DL_SYSCTL_configSYSPLL               
00003081  Get_Analog_value                     
0000315d  ldexp                                
0000315d  ldexpl                               
0000315d  scalbn                               
0000315d  scalbnl                              
00003235  __aeabi_fsub                         
00003235  __subsf3                             
0000323f  __addsf3                             
0000323f  __aeabi_fadd                         
0000330d  Motor_Start                          
000033d9  DL_Timer_initPWMMode                 
0000349d  mspm0_i2c_write                      
00003619  Motor_SetDuty                        
000036d1  Task_Add                             
00003785  SYSCFG_DL_initPower                  
00003835  normalizeAnalogValues                
000038df  __aeabi_ldiv0                        
000038e1  __udivmoddi4                         
00003985  __aeabi_fmul                         
00003985  __mulsf3                             
00003a9d  SYSCFG_DL_UART0_init                 
00003ba5  __aeabi_fdiv                         
00003ba5  __divsf3                             
00003c29  SYSCFG_DL_MotorBack_init             
00003ca9  SYSCFG_DL_MotorFront_init            
00003d29  Task_Init                            
00003da5  __TI_decompress_lzss                 
00003e21  __gedf2                              
00003e21  __gtdf2                              
00003e95  SYSCFG_DL_DMA_init                   
00003ea1  __aeabi_d2f                          
00003ea1  __truncdfsf2                         
00003f15  No_MCU_Ganv_Sensor_Init_Frist        
00003f89  MyPrintf_DMA                         
00003ff9  convertAnalogToDigital               
00004065  Task_GraySensor                      
000040cd  __cmpdf2                             
000040cd  __eqdf2                              
000040cd  __ledf2                              
000040cd  __ltdf2                              
000040cd  __nedf2                              
0000419d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004201  SYSCFG_DL_I2C_OLED_init              
00004265  mpu6050_i2c_sda_unlock               
000042c9  __aeabi_dcmpeq                       
000042dd  __aeabi_dcmplt                       
000042f1  __aeabi_dcmple                       
00004305  __aeabi_dcmpge                       
00004319  __aeabi_dcmpgt                       
0000432d  __aeabi_fcmpeq                       
00004341  __aeabi_fcmplt                       
00004355  __aeabi_fcmple                       
00004369  __aeabi_fcmpge                       
0000437d  __aeabi_fcmpgt                       
00004391  Task_IdleFunction                    
000043f1  DL_I2C_fillControllerTXFIFO          
00004451  SYSCFG_DL_SYSCTL_init                
000044ad  frexp                                
000044ad  frexpl                               
00004509  SYSCFG_DL_I2C_MPU6050_init           
00004561  Serial_Init                          
000045b9  __TI_ltoa                            
00004669  __aeabi_idiv                         
00004669  __aeabi_idivmod                      
000046c1  Interrupt_Init                       
000048a9  DL_DMA_initChannel                   
00004941  SYSCFG_DL_ADC1_init                  
000049d9  __aeabi_d2iz                         
000049d9  __fixdfsi                            
00004a25  DL_UART_init                         
00004a6d  adc_getValue                         
00004ab5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004af9  PID_IQ_SetParams                     
00004b81  No_Mcu_Ganv_Sensor_Task_Without_tick 
00004bc5  __aeabi_d2uiz                        
00004bc5  __fixunsdfsi                         
00004c09  DL_ADC12_setClockConfig              
00004c49  __aeabi_uidiv                        
00004c49  __aeabi_uidivmod                     
00004c89  __aeabi_f2d                          
00004c89  __extendsfdf2                        
00004cc9  atoi                                 
00004d09  vsnprintf                            
00004e01  DL_I2C_flushControllerTXFIFO         
00004e3d  Get_Anolog_Value                     
00004e79  __aeabi_i2f                          
00004e79  __floatsisf                          
00004eb5  __gesf2                              
00004eb5  __gtsf2                              
00004ef1  __TI_auto_init_nobinit_nopinit       
00004f2d  __cmpsf2                             
00004f2d  __eqsf2                              
00004f2d  __lesf2                              
00004f2d  __ltsf2                              
00004f2d  __nesf2                              
00004f69  __muldsi3                            
00004fa3  Get_Normalize_For_User               
00004fdd  __aeabi_f2iz                         
00004fdd  __fixsfsi                            
0000510d  SYSCFG_DL_DMA_CH_RX_init             
0000513d  SYSCFG_DL_init                       
000051f9  __aeabi_i2d                          
000051f9  __floatsidf                          
00005225  PID_IQ_Init                          
00005391  SysTick_Increasment                  
000053b9  _c_int00_noargs                      
00005453  DL_I2C_setClockConfig                
0000549d  __aeabi_ui2d                         
0000549d  __floatunsidf                        
000054c1  __aeabi_lmul                         
000054c1  __muldi3                             
000054e5  memccpy                              
00005549  Delay                                
00005569  main                                 
000055a9  __aeabi_llsl                         
000055a9  __ashldi3                            
00005751  DL_Timer_setCaptCompUpdateMethod     
0000576d  DL_Timer_setClockConfig              
00005a8d  DL_Timer_setCaptureCompareOutCtl     
00005b4d  SYSCFG_DL_DMA_CH_TX_init             
00005b65  _IQ24div                             
00005b7d  _IQ24mpy                             
00005c47  SysGetTick                           
00005c5d  __TI_zero_init_nomemset              
00005d3d  __aeabi_uldivmod                     
00005d65  DL_UART_setClockConfig               
00005d77  TI_memcpy_small                      
00005d89  __TI_decompress_none                 
00005dbd  DL_Timer_setCaptureCompareValue      
00005dcd  SYSCFG_DL_SYSTICK_init               
00005ddd  wcslen                               
00005ded  Get_Digtal_For_User                  
00005dfd  __aeabi_memset                       
00005dfd  __aeabi_memset4                      
00005dfd  __aeabi_memset8                      
00005e19  TI_memset_small                      
00005e29  Sys_GetTick                          
00005e35  DL_Common_delayCycles                
00005eb5  SysTick_Handler                      
00005ebd  __aeabi_errno_addr                   
00005ec5  __aeabi_memcpy                       
00005ec5  __aeabi_memcpy4                      
00005ec5  __aeabi_memcpy8                      
00005eeb  abort                                
00005ef1  ADC0_IRQHandler                      
00005ef1  ADC1_IRQHandler                      
00005ef1  AES_IRQHandler                       
00005ef1  CANFD0_IRQHandler                    
00005ef1  DAC0_IRQHandler                      
00005ef1  DMA_IRQHandler                       
00005ef1  Default_Handler                      
00005ef1  GROUP0_IRQHandler                    
00005ef1  HardFault_Handler                    
00005ef1  I2C0_IRQHandler                      
00005ef1  I2C1_IRQHandler                      
00005ef1  NMI_Handler                          
00005ef1  PendSV_Handler                       
00005ef1  RTC_IRQHandler                       
00005ef1  SPI0_IRQHandler                      
00005ef1  SPI1_IRQHandler                      
00005ef1  SVC_Handler                          
00005ef1  TIMA0_IRQHandler                     
00005ef1  TIMA1_IRQHandler                     
00005ef1  TIMG0_IRQHandler                     
00005ef1  TIMG12_IRQHandler                    
00005ef1  TIMG6_IRQHandler                     
00005ef1  TIMG7_IRQHandler                     
00005ef1  TIMG8_IRQHandler                     
00005ef1  UART0_IRQHandler                     
00005ef1  UART1_IRQHandler                     
00005ef1  UART2_IRQHandler                     
00005ef1  UART3_IRQHandler                     
00005ef4  C$$EXIT                              
00005ef5  HOSTexit                             
00005efd  Reset_Handler                        
00005f11  _system_pre_init                     
00005f20  __aeabi_ctype_table_                 
00005f20  __aeabi_ctype_table_C                
00006090  test                                 
000060b8  reg                                  
00006142  hw                                   
000061e0  __TI_Handler_Table_Base              
000061ec  __TI_Handler_Table_Limit             
000061f4  __TI_CINIT_Base                      
00006204  __TI_CINIT_Limit                     
00006204  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Back_Left                      
20200418  Motor_Back_Right                     
2020045c  Motor_Font_Left                      
202004a0  Motor_Font_Right                     
20200510  Gray_Anolog                          
20200520  Gray_Normal                          
20200540  Data_MotorEncoder                    
20200548  Data_Tracker_Input                   
20200550  Data_Tracker_Offset                  
20200554  __aeabi_errno                        
20200558  delayTick                            
2020055c  uwTick                               
20200564  Flag_MPU6050_Ready                   
20200565  Gray_Digtal                          
20200567  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[268 symbols]
