******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 18:10:11 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000055a1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006498  00019b68  R  X
  SRAM                  20200000   00008000  00000767  00007899  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006498   00006498    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00006050   00006050    r-x .text
  00006110    00006110    00000310   00000310    r-- .rodata
  00006420    00006420    00000078   00000078    r-- .cinit
20200000    20200000    00000568   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000194   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00006050     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    0000025c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001348    0000022c     MPU6050.o (.text.Read_Quad)
                  00001574    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000017a0    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  000019c0    000001f8     Task_App.o (.text.Debug_GraySensor_Output)
                  00001bb8    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001dac    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001f88    000001b0     Task.o (.text.Task_Start)
                  00002138    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000022ca    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000022cc    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002454    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000025dc    00000170            : e_sqrt.c.obj (.text.sqrt)
                  0000274c    0000016c     Interrupt.o (.text.GROUP1_IRQHandler)
                  000028b8    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000029f4    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002b28    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00002c5c    00000128     Task_App.o (.text.Task_Tracker)
                  00002d84    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002ea4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002fb0    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  000030b8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000319c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003278    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00003354    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000342c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00003504    000000cc     Motor.o (.text.Motor_Start)
                  000035d0    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00003694    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00003758    000000b8     Motor.o (.text.Motor_SetDirc)
                  00003810    000000b8     Motor.o (.text.Motor_SetDuty)
                  000038c8    000000b4     Task.o (.text.Task_Add)
                  0000397c    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003a2c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00003ad6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003ad8    000000a2                            : udivmoddi4.S.obj (.text)
                  00003b7a    00000002     --HOLE-- [fill = 0]
                  00003b7c    0000008c                            : mulsf3.S.obj (.text.__mulsf3)
                  00003c08    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00003c94    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00003d18    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003d9c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003e1e    00000002     --HOLE-- [fill = 0]
                  00003e20    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00003ea0    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00003f20    0000007c     Task_App.o (.text.Task_Init)
                  00003f9c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00004018    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000408c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004090    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004104    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00004176    00000002     --HOLE-- [fill = 0]
                  00004178    00000070     Serial.o (.text.MyPrintf_DMA)
                  000041e8    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00004254    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000042bc    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00004322    00000002     --HOLE-- [fill = 0]
                  00004324    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00004388    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000043ec    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00004450    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000044b2    00000002     --HOLE-- [fill = 0]
                  000044b4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00004516    00000002     --HOLE-- [fill = 0]
                  00004518    00000060     Task_App.o (.text.Task_GraySensor)
                  00004578    00000060     Task_App.o (.text.Task_IdleFunction)
                  000045d8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00004636    00000002     --HOLE-- [fill = 0]
                  00004638    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004694    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000046f0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00004748    00000058     Serial.o (.text.Serial_Init)
                  000047a0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000047f8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004850    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000048a6    00000002     --HOLE-- [fill = 0]
                  000048a8    00000054     Interrupt.o (.text.Interrupt_Init)
                  000048fc    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  00004950    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000049a2    00000002     --HOLE-- [fill = 0]
                  000049a4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000049f4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00004a44    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00004a90    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00004adc    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004b28    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00004b74    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004bbe    00000002     --HOLE-- [fill = 0]
                  00004bc0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004c0a    00000002     --HOLE-- [fill = 0]
                  00004c0c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004c54    00000048     ADC.o (.text.adc_getValue)
                  00004c9c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00004ce0    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00004d24    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00004d68    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00004daa    00000002     --HOLE-- [fill = 0]
                  00004dac    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00004dee    00000002     --HOLE-- [fill = 0]
                  00004df0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004e30    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004e70    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004eb0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004ef0    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004f30    0000003e     Task.o (.text.Task_CMP)
                  00004f6e    00000002     --HOLE-- [fill = 0]
                  00004f70    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004fac    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004fe8    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00005024    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00005060    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000509c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000050d8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00005114    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000514e    00000002     --HOLE-- [fill = 0]
                  00005150    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000518a    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  000051c2    00000002     --HOLE-- [fill = 0]
                  000051c4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000051fc    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005230    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005264    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00005294    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000052c4    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  000052f4    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00005324    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00005354    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005384    00000030            : vsnprintf.c.obj (.text._outs)
                  000053b4    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000053e0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000540c    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00005436    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  0000545e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005486    00000002     --HOLE-- [fill = 0]
                  00005488    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000054b0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000054d8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00005500    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00005528    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005550    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00005578    00000028     SysTick.o (.text.SysTick_Increasment)
                  000055a0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000055c8    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000055ee    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00005614    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000563a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005660    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00005684    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000056a8    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000056cc    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000056ee    00000002     --HOLE-- [fill = 0]
                  000056f0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005710    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005730    00000020     SysTick.o (.text.Delay)
                  00005750    00000020     main.o (.text.main)
                  00005770    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000578e    00000002     --HOLE-- [fill = 0]
                  00005790    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000057ae    00000002     --HOLE-- [fill = 0]
                  000057b0    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000057cc    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  000057e8    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00005804    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005820    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000583c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00005858    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00005874    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005890    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000058ac    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000058c8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000058e4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005900    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000591c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005938    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005954    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005970    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000598c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000059a4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000059bc    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000059d4    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000059ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005a04    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00005a1c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005a34    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00005a4c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005a64    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00005a7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005a94    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00005aac    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00005ac4    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00005adc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005af4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005b0c    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00005b24    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00005b3c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005b54    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00005b6c    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00005b84    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00005b9c    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00005bb4    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00005bcc    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005be4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00005bfc    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005c14    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00005c2c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005c44    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00005c5c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005c74    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005c8c    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00005ca4    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00005cbc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00005cd4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005cec    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005d04    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005d1c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005d34    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005d4c    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00005d64    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00005d7c    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00005d94    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00005daa    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00005dc0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005dd6    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00005dec    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00005e02    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00005e18    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005e2e    00000016     SysTick.o (.text.SysGetTick)
                  00005e44    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005e5a    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005e6e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005e82    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00005e96    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005eaa    00000002     --HOLE-- [fill = 0]
                  00005eac    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00005ec0    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00005ed4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005ee8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005efc    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005f10    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005f24    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005f38    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005f4c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005f5e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005f70    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005f82    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00005f92    00000002     --HOLE-- [fill = 0]
                  00005f94    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005fa4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005fb4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005fc4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005fd4    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00005fe2    00000002     --HOLE-- [fill = 0]
                  00005fe4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005ff2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00006000    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000600e    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000601a    00000002     --HOLE-- [fill = 0]
                  0000601c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00006028    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00006032    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000603c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000604c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006056    00000002     --HOLE-- [fill = 0]
                  00006058    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00006068    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006072    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000607c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006086    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00006090    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000060a0    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000060a8    00000008     Interrupt.o (.text.SysTick_Handler)
                  000060b0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000060b8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000060c0    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000060c6    00000002     --HOLE-- [fill = 0]
                  000060c8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000060d8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000060de    00000006            : exit.c.obj (.text:abort)
                  000060e4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000060e8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000060ec    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000060f0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00006100    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00006104    0000000c     --HOLE-- [fill = 0]

.cinit     0    00006420    00000078     
                  00006420    00000050     (.cinit..data.load) [load image, compression = lzss]
                  00006470    0000000c     (__TI_handler_table)
                  0000647c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006484    00000010     (__TI_cinit_table)
                  00006494    00000004     --HOLE-- [fill = 0]

.rodata    0    00006110    00000310     
                  00006110    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00006211    00000006     Task_App.o (.rodata.str1.13861004553356644102.1)
                  00006217    00000001     --HOLE-- [fill = 0]
                  00006218    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00006258    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00006280    00000028     inv_mpu.o (.rodata.test)
                  000062a8    0000001e     inv_mpu.o (.rodata.reg)
                  000062c6    0000001c     Task_App.o (.rodata.str1.5883415095785080416.1)
                  000062e2    0000001b     Task_App.o (.rodata.str1.4133793139133961309.1)
                  000062fd    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00006300    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00006318    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00006330    00000018     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00006348    00000012     Task_App.o (.rodata.str1.3850258909703972507.1)
                  0000635a    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000636b    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000637c    0000000c     inv_mpu.o (.rodata.hw)
                  00006388    0000000b     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00006393    00000001     --HOLE-- [fill = 0]
                  00006394    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000639e    0000000a     Task_App.o (.rodata.str1.1200745476254391468.1)
                  000063a8    0000000a     Task_App.o (.rodata.str1.12980382611605970010.1)
                  000063b2    0000000a     Task_App.o (.rodata.str1.13166305789289702848.1)
                  000063bc    0000000a     Task_App.o (.rodata.str1.5017135634981511656.1)
                  000063c6    0000000a     Task_App.o (.rodata.str1.5297265082290894444.1)
                  000063d0    0000000a     Task_App.o (.rodata.str1.9092480108036065651.1)
                  000063da    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000063dc    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  000063e4    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  000063ec    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000063f4    00000008     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000063fc    00000005     Task_App.o (.rodata.str1.5161480910995489644.1)
                  00006401    00000005     Task_App.o (.rodata.str1.7346008805793267871.1)
                  00006406    00000004     Task_App.o (.rodata.str1.16301319874972139807.1)
                  0000640a    00000004     Task_App.o (.rodata.str1.7950429023856218820.1)
                  0000640e    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00006411    00000003     Task_App.o (.rodata.str1.11373919790952722939.1)
                  00006414    00000003     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00006417    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00006419    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000641b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000194     UNINITIALIZED
                  202003d4    00000044     Motor.o (.data.Motor_Back_Left)
                  20200418    00000044     Motor.o (.data.Motor_Back_Right)
                  2020045c    00000044     Motor.o (.data.Motor_Font_Left)
                  202004a0    00000044     Motor.o (.data.Motor_Font_Right)
                  202004e4    0000002c     inv_mpu.o (.data.st)
                  20200510    00000010     Task_App.o (.data.Gray_Anolog)
                  20200520    00000010     Task_App.o (.data.Gray_Normal)
                  20200530    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200540    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200548    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200550    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200554    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200558    00000004     SysTick.o (.data.delayTick)
                  2020055c    00000004     SysTick.o (.data.uwTick)
                  20200560    00000002     Task_App.o (.data.Task_GraySensor.debug_cnt)
                  20200562    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200564    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200565    00000001     Task_App.o (.data.Gray_Digtal)
                  20200566    00000001     Task.o (.data.Task_Num)
                  20200567    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3674    134       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3714    326       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1116    206       233    
       Interrupt.o                      626     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1742    206       239    
                                                                 
    .\BSP\Src\
       MPU6050.o                        1872    0         47     
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          640     0         272    
       ADC.o                            236     0         0      
       SysTick.o                        106     0         8      
       PID_IQMath.o                     110     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5286    0         1080   
                                                                 
    .\DMP\
       inv_mpu.o                        820     82        44     
       inv_mpu_dmp_motion_driver.o      640     0         16     
    +--+--------------------------------+-------+---------+---------+
       Total:                           1460    82        60     
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       292     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1112    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           48      0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8246    355       4      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2980    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     24592   1085      1895   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006484 records: 2, size/record: 8, table size: 16
	.data: load addr=00006420, load size=00000050 bytes, run addr=202003d4, run size=00000194 bytes, compression=lzss
	.bss: load addr=0000647c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006470 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002139     0000603c     0000603a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000030b9     00006058     00006054   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006070          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006084          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000060a6          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000060dc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002ea5     00006090     0000608e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002143     000060c8     000060c4   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000060ea          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000055a1     000060f0     000060ec   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000408d  ADC0_IRQHandler                      
0000408d  ADC1_IRQHandler                      
0000408d  AES_IRQHandler                       
000060e4  C$$EXIT                              
0000408d  CANFD0_IRQHandler                    
0000408d  DAC0_IRQHandler                      
00004df1  DL_ADC12_setClockConfig              
00006029  DL_Common_delayCycles                
00004a91  DL_DMA_initChannel                   
000045d9  DL_I2C_fillControllerTXFIFO          
00004fe9  DL_I2C_flushControllerTXFIFO         
0000563b  DL_I2C_setClockConfig                
0000319d  DL_SYSCTL_configSYSPLL               
00004325  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004c9d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000035d1  DL_Timer_initPWMMode                 
00005939  DL_Timer_setCaptCompUpdateMethod     
00005c75  DL_Timer_setCaptureCompareOutCtl     
00005fa5  DL_Timer_setCaptureCompareValue      
00005955  DL_Timer_setClockConfig              
00004c0d  DL_UART_init                         
00005f4d  DL_UART_setClockConfig               
0000408d  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
20200540  Data_MotorEncoder                    
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
20200548  Data_Tracker_Input                   
20200550  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
000019c1  Debug_GraySensor_Output              
0000408d  Default_Handler                      
00005731  Delay                                
202003c8  ExISR_Flag                           
20200564  Flag_MPU6050_Ready                   
0000408d  GROUP0_IRQHandler                    
0000274d  GROUP1_IRQHandler                    
00003279  Get_Analog_value                     
00005025  Get_Anolog_Value                     
00005fd5  Get_Digtal_For_User                  
0000518b  Get_Normalize_For_User               
202002f0  GraySensor                           
20200510  Gray_Anolog                          
20200565  Gray_Digtal                          
20200520  Gray_Normal                          
000060e5  HOSTexit                             
0000408d  HardFault_Handler                    
0000408d  I2C0_IRQHandler                      
0000408d  I2C1_IRQHandler                      
000048a9  Interrupt_Init                       
202003d4  Motor_Back_Left                      
20200418  Motor_Back_Right                     
2020045c  Motor_Font_Left                      
202004a0  Motor_Font_Right                     
00003811  Motor_SetDuty                        
00003505  Motor_Start                          
00004179  MyPrintf_DMA                         
0000408d  NMI_Handler                          
000022cd  No_MCU_Ganv_Sensor_Init              
00004105  No_MCU_Ganv_Sensor_Init_Frist        
00004d69  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000540d  PID_IQ_Init                          
00004ce1  PID_IQ_SetParams                     
0000408d  PendSV_Handler                       
0000408d  RTC_IRQHandler                       
00001349  Read_Quad                            
000060ed  Reset_Handler                        
0000408d  SPI0_IRQHandler                      
0000408d  SPI1_IRQHandler                      
0000408d  SVC_Handler                          
00004b29  SYSCFG_DL_ADC1_init                  
000052f5  SYSCFG_DL_DMA_CH_RX_init             
00005d35  SYSCFG_DL_DMA_CH_TX_init             
0000600f  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
000046f1  SYSCFG_DL_I2C_MPU6050_init           
00004389  SYSCFG_DL_I2C_OLED_init              
00003e21  SYSCFG_DL_MotorBack_init             
00003ea1  SYSCFG_DL_MotorFront_init            
00004639  SYSCFG_DL_SYSCTL_init                
00005fb5  SYSCFG_DL_SYSTICK_init               
00003c95  SYSCFG_DL_UART0_init                 
00005325  SYSCFG_DL_init                       
0000397d  SYSCFG_DL_initPower                  
00004749  Serial_Init                          
20200000  Serial_RxData                        
00005e2f  SysGetTick                           
000060a9  SysTick_Handler                      
00005579  SysTick_Increasment                  
0000601d  Sys_GetTick                          
0000408d  TIMA0_IRQHandler                     
0000408d  TIMA1_IRQHandler                     
0000408d  TIMG0_IRQHandler                     
0000408d  TIMG12_IRQHandler                    
0000408d  TIMG6_IRQHandler                     
0000408d  TIMG7_IRQHandler                     
0000408d  TIMG8_IRQHandler                     
00005f5f  TI_memcpy_small                      
00006001  TI_memset_small                      
000038c9  Task_Add                             
00004519  Task_GraySensor                      
00004579  Task_IdleFunction                    
00003f21  Task_Init                            
00001f89  Task_Start                           
00002c5d  Task_Tracker                         
0000408d  UART0_IRQHandler                     
0000408d  UART1_IRQHandler                     
0000408d  UART2_IRQHandler                     
0000408d  UART3_IRQHandler                     
00005d4d  _IQ24div                             
00005d65  _IQ24mpy                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006484  __TI_CINIT_Base                      
00006494  __TI_CINIT_Limit                     
00006494  __TI_CINIT_Warm                      
00006470  __TI_Handler_Table_Base              
0000647c  __TI_Handler_Table_Limit             
000050d9  __TI_auto_init_nobinit_nopinit       
00003f9d  __TI_decompress_lzss                 
00005f71  __TI_decompress_none                 
000047a1  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005e45  __TI_zero_init_nomemset              
00002143  __adddf3                             
00003437  __addsf3                             
00006110  __aeabi_ctype_table_                 
00006110  __aeabi_ctype_table_C                
00004091  __aeabi_d2f                          
00004bc1  __aeabi_d2iz                         
00004dad  __aeabi_d2uiz                        
00002143  __aeabi_dadd                         
00004451  __aeabi_dcmpeq                       
0000448d  __aeabi_dcmpge                       
000044a1  __aeabi_dcmpgt                       
00004479  __aeabi_dcmple                       
00004465  __aeabi_dcmplt                       
00002ea5  __aeabi_ddiv                         
000030b9  __aeabi_dmul                         
00002139  __aeabi_dsub                         
20200554  __aeabi_errno                        
000060b1  __aeabi_errno_addr                   
00004e71  __aeabi_f2d                          
000051c5  __aeabi_f2iz                         
00003437  __aeabi_fadd                         
000044b5  __aeabi_fcmpeq                       
000044f1  __aeabi_fcmpge                       
00004505  __aeabi_fcmpgt                       
000044dd  __aeabi_fcmple                       
000044c9  __aeabi_fcmplt                       
00003d9d  __aeabi_fdiv                         
00003b7d  __aeabi_fmul                         
0000342d  __aeabi_fsub                         
000053e1  __aeabi_i2d                          
00005061  __aeabi_i2f                          
00004851  __aeabi_idiv                         
000022cb  __aeabi_idiv0                        
00004851  __aeabi_idivmod                      
00003ad7  __aeabi_ldiv0                        
00005791  __aeabi_llsl                         
000056a9  __aeabi_lmul                         
000060b9  __aeabi_memcpy                       
000060b9  __aeabi_memcpy4                      
000060b9  __aeabi_memcpy8                      
00005fe5  __aeabi_memset                       
00005fe5  __aeabi_memset4                      
00005fe5  __aeabi_memset8                      
00005685  __aeabi_ui2d                         
00004e31  __aeabi_uidiv                        
00004e31  __aeabi_uidivmod                     
00005f25  __aeabi_uldivmod                     
00005791  __ashldi3                            
ffffffff  __binit__                            
00004255  __cmpdf2                             
00005115  __cmpsf2                             
00002ea5  __divdf3                             
00003d9d  __divsf3                             
00004255  __eqdf2                              
00005115  __eqsf2                              
00004e71  __extendsfdf2                        
00004bc1  __fixdfsi                            
000051c5  __fixsfsi                            
00004dad  __fixunsdfsi                         
000053e1  __floatsidf                          
00005061  __floatsisf                          
00005685  __floatunsidf                        
00004019  __gedf2                              
0000509d  __gesf2                              
00004019  __gtdf2                              
0000509d  __gtsf2                              
00004255  __ledf2                              
00005115  __lesf2                              
00004255  __ltdf2                              
00005115  __ltsf2                              
UNDEFED   __mpu_init                           
000030b9  __muldf3                             
000056a9  __muldi3                             
00005151  __muldsi3                            
00003b7d  __mulsf3                             
00004255  __nedf2                              
00005115  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002139  __subdf3                             
0000342d  __subsf3                             
00004091  __truncdfsf2                         
00003ad9  __udivmoddi4                         
000055a1  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00006101  _system_pre_init                     
000060df  abort                                
00004c55  adc_getValue                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002455  atan2                                
00002455  atan2l                               
00000df5  atanl                                
00004eb1  atoi                                 
ffffffff  binit                                
000041e9  convertAnalogToDigital               
20200558  delayTick                            
00001bb9  dmp_read_fifo                        
20200567  enable_group1_irq                    
00004695  frexp                                
00004695  frexpl                               
0000637c  hw                                   
00000000  interruptVectors                     
00003355  ldexp                                
00003355  ldexpl                               
00005751  main                                 
000056cd  memccpy                              
202003d2  more                                 
000043ed  mpu6050_i2c_sda_unlock               
00002fb1  mpu_read_fifo_stream                 
00001575  mpu_reset_fifo                       
000029f5  mspm0_i2c_read                       
00003695  mspm0_i2c_write                      
00003a2d  normalizeAnalogValues                
00002b29  qsort                                
202003a0  quat                                 
000062a8  reg                                  
00003355  scalbn                               
00003355  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
000025dd  sqrt                                 
000025dd  sqrtl                                
00006280  test                                 
2020055c  uwTick                               
00004ef1  vsnprintf                            
00005fc5  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
00001349  Read_Quad                            
00001575  mpu_reset_fifo                       
000019c1  Debug_GraySensor_Output              
00001bb9  dmp_read_fifo                        
00001f89  Task_Start                           
00002139  __aeabi_dsub                         
00002139  __subdf3                             
00002143  __adddf3                             
00002143  __aeabi_dadd                         
000022cb  __aeabi_idiv0                        
000022cd  No_MCU_Ganv_Sensor_Init              
00002455  atan2                                
00002455  atan2l                               
000025dd  sqrt                                 
000025dd  sqrtl                                
0000274d  GROUP1_IRQHandler                    
000029f5  mspm0_i2c_read                       
00002b29  qsort                                
00002c5d  Task_Tracker                         
00002ea5  __aeabi_ddiv                         
00002ea5  __divdf3                             
00002fb1  mpu_read_fifo_stream                 
000030b9  __aeabi_dmul                         
000030b9  __muldf3                             
0000319d  DL_SYSCTL_configSYSPLL               
00003279  Get_Analog_value                     
00003355  ldexp                                
00003355  ldexpl                               
00003355  scalbn                               
00003355  scalbnl                              
0000342d  __aeabi_fsub                         
0000342d  __subsf3                             
00003437  __addsf3                             
00003437  __aeabi_fadd                         
00003505  Motor_Start                          
000035d1  DL_Timer_initPWMMode                 
00003695  mspm0_i2c_write                      
00003811  Motor_SetDuty                        
000038c9  Task_Add                             
0000397d  SYSCFG_DL_initPower                  
00003a2d  normalizeAnalogValues                
00003ad7  __aeabi_ldiv0                        
00003ad9  __udivmoddi4                         
00003b7d  __aeabi_fmul                         
00003b7d  __mulsf3                             
00003c95  SYSCFG_DL_UART0_init                 
00003d9d  __aeabi_fdiv                         
00003d9d  __divsf3                             
00003e21  SYSCFG_DL_MotorBack_init             
00003ea1  SYSCFG_DL_MotorFront_init            
00003f21  Task_Init                            
00003f9d  __TI_decompress_lzss                 
00004019  __gedf2                              
00004019  __gtdf2                              
0000408d  ADC0_IRQHandler                      
0000408d  ADC1_IRQHandler                      
0000408d  AES_IRQHandler                       
0000408d  CANFD0_IRQHandler                    
0000408d  DAC0_IRQHandler                      
0000408d  DMA_IRQHandler                       
0000408d  Default_Handler                      
0000408d  GROUP0_IRQHandler                    
0000408d  HardFault_Handler                    
0000408d  I2C0_IRQHandler                      
0000408d  I2C1_IRQHandler                      
0000408d  NMI_Handler                          
0000408d  PendSV_Handler                       
0000408d  RTC_IRQHandler                       
0000408d  SPI0_IRQHandler                      
0000408d  SPI1_IRQHandler                      
0000408d  SVC_Handler                          
0000408d  TIMA0_IRQHandler                     
0000408d  TIMA1_IRQHandler                     
0000408d  TIMG0_IRQHandler                     
0000408d  TIMG12_IRQHandler                    
0000408d  TIMG6_IRQHandler                     
0000408d  TIMG7_IRQHandler                     
0000408d  TIMG8_IRQHandler                     
0000408d  UART0_IRQHandler                     
0000408d  UART1_IRQHandler                     
0000408d  UART2_IRQHandler                     
0000408d  UART3_IRQHandler                     
00004091  __aeabi_d2f                          
00004091  __truncdfsf2                         
00004105  No_MCU_Ganv_Sensor_Init_Frist        
00004179  MyPrintf_DMA                         
000041e9  convertAnalogToDigital               
00004255  __cmpdf2                             
00004255  __eqdf2                              
00004255  __ledf2                              
00004255  __ltdf2                              
00004255  __nedf2                              
00004325  DL_SYSCTL_setHFCLKSourceHFXTParams   
00004389  SYSCFG_DL_I2C_OLED_init              
000043ed  mpu6050_i2c_sda_unlock               
00004451  __aeabi_dcmpeq                       
00004465  __aeabi_dcmplt                       
00004479  __aeabi_dcmple                       
0000448d  __aeabi_dcmpge                       
000044a1  __aeabi_dcmpgt                       
000044b5  __aeabi_fcmpeq                       
000044c9  __aeabi_fcmplt                       
000044dd  __aeabi_fcmple                       
000044f1  __aeabi_fcmpge                       
00004505  __aeabi_fcmpgt                       
00004519  Task_GraySensor                      
00004579  Task_IdleFunction                    
000045d9  DL_I2C_fillControllerTXFIFO          
00004639  SYSCFG_DL_SYSCTL_init                
00004695  frexp                                
00004695  frexpl                               
000046f1  SYSCFG_DL_I2C_MPU6050_init           
00004749  Serial_Init                          
000047a1  __TI_ltoa                            
00004851  __aeabi_idiv                         
00004851  __aeabi_idivmod                      
000048a9  Interrupt_Init                       
00004a91  DL_DMA_initChannel                   
00004b29  SYSCFG_DL_ADC1_init                  
00004bc1  __aeabi_d2iz                         
00004bc1  __fixdfsi                            
00004c0d  DL_UART_init                         
00004c55  adc_getValue                         
00004c9d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004ce1  PID_IQ_SetParams                     
00004d69  No_Mcu_Ganv_Sensor_Task_Without_tick 
00004dad  __aeabi_d2uiz                        
00004dad  __fixunsdfsi                         
00004df1  DL_ADC12_setClockConfig              
00004e31  __aeabi_uidiv                        
00004e31  __aeabi_uidivmod                     
00004e71  __aeabi_f2d                          
00004e71  __extendsfdf2                        
00004eb1  atoi                                 
00004ef1  vsnprintf                            
00004fe9  DL_I2C_flushControllerTXFIFO         
00005025  Get_Anolog_Value                     
00005061  __aeabi_i2f                          
00005061  __floatsisf                          
0000509d  __gesf2                              
0000509d  __gtsf2                              
000050d9  __TI_auto_init_nobinit_nopinit       
00005115  __cmpsf2                             
00005115  __eqsf2                              
00005115  __lesf2                              
00005115  __ltsf2                              
00005115  __nesf2                              
00005151  __muldsi3                            
0000518b  Get_Normalize_For_User               
000051c5  __aeabi_f2iz                         
000051c5  __fixsfsi                            
000052f5  SYSCFG_DL_DMA_CH_RX_init             
00005325  SYSCFG_DL_init                       
000053e1  __aeabi_i2d                          
000053e1  __floatsidf                          
0000540d  PID_IQ_Init                          
00005579  SysTick_Increasment                  
000055a1  _c_int00_noargs                      
0000563b  DL_I2C_setClockConfig                
00005685  __aeabi_ui2d                         
00005685  __floatunsidf                        
000056a9  __aeabi_lmul                         
000056a9  __muldi3                             
000056cd  memccpy                              
00005731  Delay                                
00005751  main                                 
00005791  __aeabi_llsl                         
00005791  __ashldi3                            
00005939  DL_Timer_setCaptCompUpdateMethod     
00005955  DL_Timer_setClockConfig              
00005c75  DL_Timer_setCaptureCompareOutCtl     
00005d35  SYSCFG_DL_DMA_CH_TX_init             
00005d4d  _IQ24div                             
00005d65  _IQ24mpy                             
00005e2f  SysGetTick                           
00005e45  __TI_zero_init_nomemset              
00005f25  __aeabi_uldivmod                     
00005f4d  DL_UART_setClockConfig               
00005f5f  TI_memcpy_small                      
00005f71  __TI_decompress_none                 
00005fa5  DL_Timer_setCaptureCompareValue      
00005fb5  SYSCFG_DL_SYSTICK_init               
00005fc5  wcslen                               
00005fd5  Get_Digtal_For_User                  
00005fe5  __aeabi_memset                       
00005fe5  __aeabi_memset4                      
00005fe5  __aeabi_memset8                      
00006001  TI_memset_small                      
0000600f  SYSCFG_DL_DMA_init                   
0000601d  Sys_GetTick                          
00006029  DL_Common_delayCycles                
000060a9  SysTick_Handler                      
000060b1  __aeabi_errno_addr                   
000060b9  __aeabi_memcpy                       
000060b9  __aeabi_memcpy4                      
000060b9  __aeabi_memcpy8                      
000060df  abort                                
000060e4  C$$EXIT                              
000060e5  HOSTexit                             
000060ed  Reset_Handler                        
00006101  _system_pre_init                     
00006110  __aeabi_ctype_table_                 
00006110  __aeabi_ctype_table_C                
00006280  test                                 
000062a8  reg                                  
0000637c  hw                                   
00006470  __TI_Handler_Table_Base              
0000647c  __TI_Handler_Table_Limit             
00006484  __TI_CINIT_Base                      
00006494  __TI_CINIT_Limit                     
00006494  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Back_Left                      
20200418  Motor_Back_Right                     
2020045c  Motor_Font_Left                      
202004a0  Motor_Font_Right                     
20200510  Gray_Anolog                          
20200520  Gray_Normal                          
20200540  Data_MotorEncoder                    
20200548  Data_Tracker_Input                   
20200550  Data_Tracker_Offset                  
20200554  __aeabi_errno                        
20200558  delayTick                            
2020055c  uwTick                               
20200564  Flag_MPU6050_Ready                   
20200565  Gray_Digtal                          
20200567  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[269 symbols]
