******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 16:45:46 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000039f5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004528  0001bad8  R  X
  SRAM                  20200000   00008000  000006a0  00007960  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004528   00004528    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000042c0   000042c0    r-x .text
  00004380    00004380    00000120   00000120    r-- .rodata
  000044a0    000044a0    00000088   00000088    r-- .cinit
20200000    20200000    000004a1   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    0000017d   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000042c0     
                  000000c0    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000424    000002f8            : s_atan.c.obj (.text.atan)
                  0000071c    00000258     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000974    0000022c     MPU6050.o (.text.Read_Quad)
                  00000ba0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00000dcc    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00000fc0    000001b0     Task.o (.text.Task_Start)
                  00001170    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001302    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001304    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  0000148c    00000170            : e_sqrt.c.obj (.text.sqrt)
                  000015fc    0000016c     Interrupt.o (.text.GROUP1_IRQHandler)
                  00001768    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000189c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000019d0    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00001af4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001c00    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00001d08    00000104     Task_App.o (.text.Task_Motor_PID)
                  00001e0c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001ef0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001fcc    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000020a4    000000cc     Motor.o (.text.Motor_Start)
                  00002170    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00002234    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  000022f8    000000b8     Motor.o (.text.Motor_SetDirc)
                  000023b0    000000b8     Motor.o (.text.Motor_SetDuty)
                  00002468    000000b4     Task.o (.text.Task_Add)
                  0000251c    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000025cc    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00002670    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000026fc    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00002788    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  0000280c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002890    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002912    00000002     --HOLE-- [fill = 0]
                  00002914    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00002994    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00002a14    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002a90    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002b04    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00002b10    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002b84    00000068                            : comparedf2.c.obj (.text.__ledf2)
                  00002bec    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002c50    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002cb4    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00002d18    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002d7a    00000002     --HOLE-- [fill = 0]
                  00002d7c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002dde    00000002     --HOLE-- [fill = 0]
                  00002de0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00002e40    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002e9e    00000002     --HOLE-- [fill = 0]
                  00002ea0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002efc    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00002f54    00000058     Serial.o (.text.Serial_Init)
                  00002fac    00000054     Interrupt.o (.text.Interrupt_Init)
                  00003000    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  00003054    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000030a4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000030f4    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00003140    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000318c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000031d8    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003224    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000326e    00000002     --HOLE-- [fill = 0]
                  00003270    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000032b8    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000032fc    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00003340    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00003384    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000033c6    00000002     --HOLE-- [fill = 0]
                  000033c8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003408    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003448    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003488    0000003e     Task.o (.text.Task_CMP)
                  000034c6    00000002     --HOLE-- [fill = 0]
                  000034c8    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003504    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003540    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000357c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000035b8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000035f4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003630    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000366a    00000002     --HOLE-- [fill = 0]
                  0000366c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000036a6    00000002     --HOLE-- [fill = 0]
                  000036a8    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000036e0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003714    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003748    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00003778    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000037a8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000037d8    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003808    0000002c     Task_App.o (.text.Task_Init)
                  00003834    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003860    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000388a    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  000038b2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000038da    00000002     --HOLE-- [fill = 0]
                  000038dc    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00003904    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  0000392c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003954    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000397c    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000039a4    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000039cc    00000028     SysTick.o (.text.SysTick_Increasment)
                  000039f4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003a1c    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00003a42    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00003a68    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003a8e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003ab4    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00003ad8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003af8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003b18    00000020     SysTick.o (.text.Delay)
                  00003b38    00000020     main.o (.text.main)
                  00003b58    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003b76    00000002     --HOLE-- [fill = 0]
                  00003b78    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00003b94    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003bb0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003bcc    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00003be8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003c04    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003c20    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003c3c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00003c58    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00003c74    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00003c90    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003cac    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003cc8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003ce4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003d00    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003d1c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003d34    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003d4c    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00003d64    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00003d7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003d94    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003dac    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003dc4    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00003ddc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003df4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00003e0c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003e24    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00003e3c    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00003e54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00003e6c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003e84    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00003e9c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003eb4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003ecc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003ee4    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00003efc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003f14    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00003f2c    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00003f44    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003f5c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003f74    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00003f8c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00003fa4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003fbc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003fd4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00003fec    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004004    00000018     Motor.o (.text.DL_Timer_startCounter)
                  0000401c    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00004034    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000404c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00004064    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  0000407c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004094    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000040ac    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000040c4    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000040dc    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000040f4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000410a    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004120    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00004136    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  0000414c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00004162    00000016     SysTick.o (.text.SysGetTick)
                  00004178    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000418e    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000041a2    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000041b6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000041ca    00000002     --HOLE-- [fill = 0]
                  000041cc    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000041e0    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000041f4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004208    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000421c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004230    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004244    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004256    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004268    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000427a    00000002     --HOLE-- [fill = 0]
                  0000427c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000428c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000429c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000042ac    0000000c     SysTick.o (.text.Sys_GetTick)
                  000042b8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000042c2    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000042cc    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000042dc    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000042e6    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000042f0    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000042fa    00000002     --HOLE-- [fill = 0]
                  000042fc    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  0000430c    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004314    00000008     Interrupt.o (.text.SysTick_Handler)
                  0000431c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004324    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000432c    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004332    00000002     --HOLE-- [fill = 0]
                  00004334    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00004344    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000434a    00000006            : exit.c.obj (.text:abort)
                  00004350    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004354    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004358    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  0000435c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004360    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004370    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00004374    0000000c     --HOLE-- [fill = 0]

.cinit     0    000044a0    00000088     
                  000044a0    00000064     (.cinit..data.load) [load image, compression = lzss]
                  00004504    0000000c     (__TI_handler_table)
                  00004510    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004518    00000010     (__TI_cinit_table)

.rodata    0    00004380    00000120     
                  00004380    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000043c0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000043e8    00000028     inv_mpu.o (.rodata.test)
                  00004410    0000001e     inv_mpu.o (.rodata.reg)
                  0000442e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00004430    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00004448    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00004460    0000000c     inv_mpu.o (.rodata.hw)
                  0000446c    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00004476    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004478    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00004480    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00004488    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00004490    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00004496    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00004499    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  0000449c    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000449e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    0000017d     UNINITIALIZED
                  20200324    00000044     Motor.o (.data.Motor_Back_Left)
                  20200368    00000044     Motor.o (.data.Motor_Back_Right)
                  202003ac    00000044     Motor.o (.data.Motor_Font_Left)
                  202003f0    00000044     Motor.o (.data.Motor_Font_Right)
                  20200434    0000002c     inv_mpu.o (.data.st)
                  20200460    00000010     Task_App.o (.data.Motor)
                  20200470    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200480    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200488    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  2020048c    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200490    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200494    00000004     SysTick.o (.data.delayTick)
                  20200498    00000004     SysTick.o (.data.uwTick)
                  2020049c    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  2020049e    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  2020049f    00000001     Task.o (.data.Task_Num)
                  202004a0    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3622    134       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3662    326       0      
                                                               
    .\APP\Src\
       Interrupt.o                    626     0         6      
       Task_App.o                     400     6         34     
    +--+------------------------------+-------+---------+---------+
       Total:                         1026    6         40     
                                                               
    .\BSP\Src\
       MPU6050.o                      1872    0         47     
       Motor.o                        804     0         272    
       Task.o                         674     0         241    
       Serial.o                       292     0         512    
       PID_IQMath.o                   402     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         4150    0         1080   
                                                               
    .\DMP\
       inv_mpu.o                      820     82        44     
       inv_mpu_dmp_motion_driver.o    640     0         16     
    +--+------------------------------+-------+---------+---------+
       Total:                         1460    82        60     
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     292     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         0      
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3076    64        4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2460    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       136       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17046   614       1696   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004518 records: 2, size/record: 8, table size: 16
	.data: load addr=000044a0, load size=00000064 bytes, run addr=20200324, run size=0000017d bytes, compression=lzss
	.bss: load addr=00004510, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004504 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00001e0d     000042cc     000042ca   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000042e4          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000042ee          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004312          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00004348          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00001af5     000042fc     000042f8   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000117b     00004334     00004330   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000435a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000039f5     00004360     0000435c   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[4 trampolines]
[9 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004351  ADC0_IRQHandler                      
00004351  ADC1_IRQHandler                      
00004351  AES_IRQHandler                       
00004354  C$$EXIT                              
00004351  CANFD0_IRQHandler                    
00004351  DAC0_IRQHandler                      
000033c9  DL_ADC12_setClockConfig              
000042b9  DL_Common_delayCycles                
00003141  DL_DMA_initChannel                   
00002e41  DL_I2C_fillControllerTXFIFO          
00003541  DL_I2C_flushControllerTXFIFO         
00003a8f  DL_I2C_setClockConfig                
00001ef1  DL_SYSCTL_configSYSPLL               
00002bed  DL_SYSCTL_setHFCLKSourceHFXTParams   
000032b9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002171  DL_Timer_initPWMMode                 
00003cc9  DL_Timer_setCaptCompUpdateMethod     
00003fed  DL_Timer_setCaptureCompareOutCtl     
0000428d  DL_Timer_setCaptureCompareValue      
00003ce5  DL_Timer_setClockConfig              
00003271  DL_UART_init                         
00004245  DL_UART_setClockConfig               
00004351  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200480  Data_MotorEncoder                    
20200488  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
2020048c  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00004351  Default_Handler                      
00003b19  Delay                                
20200318  ExISR_Flag                           
2020049e  Flag_MPU6050_Ready                   
00004351  GROUP0_IRQHandler                    
000015fd  GROUP1_IRQHandler                    
00004355  HOSTexit                             
00004351  HardFault_Handler                    
00004351  I2C0_IRQHandler                      
00004351  I2C1_IRQHandler                      
00002fad  Interrupt_Init                       
20200460  Motor                                
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
000025cd  Motor_GetSpeed                       
000023b1  Motor_SetDuty                        
000020a5  Motor_Start                          
00004351  NMI_Handler                          
00003861  PID_IQ_Init                          
000019d1  PID_IQ_Prosc                         
000032fd  PID_IQ_SetParams                     
00004351  PendSV_Handler                       
00004351  RTC_IRQHandler                       
00000975  Read_Quad                            
0000435d  Reset_Handler                        
00004351  SPI0_IRQHandler                      
00004351  SPI1_IRQHandler                      
00004351  SVC_Handler                          
000031d9  SYSCFG_DL_ADC1_init                  
00003779  SYSCFG_DL_DMA_CH_RX_init             
000040ad  SYSCFG_DL_DMA_CH_TX_init             
00002b05  SYSCFG_DL_DMA_init                   
0000071d  SYSCFG_DL_GPIO_init                  
00002efd  SYSCFG_DL_I2C_MPU6050_init           
00002c51  SYSCFG_DL_I2C_OLED_init              
00002915  SYSCFG_DL_MotorBack_init             
00002995  SYSCFG_DL_MotorFront_init            
00002ea1  SYSCFG_DL_SYSCTL_init                
0000429d  SYSCFG_DL_SYSTICK_init               
00002789  SYSCFG_DL_UART0_init                 
000037a9  SYSCFG_DL_init                       
0000251d  SYSCFG_DL_initPower                  
00002f55  Serial_Init                          
20200000  Serial_RxData                        
00004163  SysGetTick                           
00004315  SysTick_Handler                      
000039cd  SysTick_Increasment                  
000042ad  Sys_GetTick                          
00004351  TIMA0_IRQHandler                     
00004351  TIMA1_IRQHandler                     
00004351  TIMG0_IRQHandler                     
00004351  TIMG12_IRQHandler                    
00004351  TIMG6_IRQHandler                     
00004351  TIMG7_IRQHandler                     
00004351  TIMG8_IRQHandler                     
00004257  TI_memcpy_small                      
00002469  Task_Add                             
00002de1  Task_IdleFunction                    
00003809  Task_Init                            
00001d09  Task_Motor_PID                       
00000fc1  Task_Start                           
00004351  UART0_IRQHandler                     
00004351  UART1_IRQHandler                     
00004351  UART2_IRQHandler                     
00004351  UART3_IRQHandler                     
000040c5  _IQ24div                             
000040dd  _IQ24mpy                             
000037d9  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004518  __TI_CINIT_Base                      
00004528  __TI_CINIT_Limit                     
00004528  __TI_CINIT_Warm                      
00004504  __TI_Handler_Table_Base              
00004510  __TI_Handler_Table_Limit             
000035f5  __TI_auto_init_nobinit_nopinit       
00002a15  __TI_decompress_lzss                 
00004269  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004179  __TI_zero_init_nomemset              
0000117b  __adddf3                             
00001fd7  __addsf3                             
00002b11  __aeabi_d2f                          
00003385  __aeabi_d2uiz                        
0000117b  __aeabi_dadd                         
00002d19  __aeabi_dcmpeq                       
00002d55  __aeabi_dcmpge                       
00002d69  __aeabi_dcmpgt                       
00002d41  __aeabi_dcmple                       
00002d2d  __aeabi_dcmplt                       
00001af5  __aeabi_ddiv                         
00001e0d  __aeabi_dmul                         
00001171  __aeabi_dsub                         
20200490  __aeabi_errno                        
0000431d  __aeabi_errno_addr                   
00003449  __aeabi_f2d                          
000036a9  __aeabi_f2iz                         
00001fd7  __aeabi_fadd                         
00002d7d  __aeabi_fcmpeq                       
00002db9  __aeabi_fcmpge                       
00002dcd  __aeabi_fcmpgt                       
00002da5  __aeabi_fcmple                       
00002d91  __aeabi_fcmplt                       
00002891  __aeabi_fdiv                         
00002671  __aeabi_fmul                         
00001fcd  __aeabi_fsub                         
0000357d  __aeabi_i2f                          
00001303  __aeabi_idiv0                        
00004325  __aeabi_memcpy                       
00004325  __aeabi_memcpy4                      
00004325  __aeabi_memcpy8                      
00003409  __aeabi_uidiv                        
00003409  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002b85  __cmpdf2                             
00003631  __cmpsf2                             
00001af5  __divdf3                             
00002891  __divsf3                             
00002b85  __eqdf2                              
00003631  __eqsf2                              
00003449  __extendsfdf2                        
000036a9  __fixsfsi                            
00003385  __fixunsdfsi                         
0000357d  __floatsisf                          
00002a91  __gedf2                              
000035b9  __gesf2                              
00002a91  __gtdf2                              
000035b9  __gtsf2                              
00002b85  __ledf2                              
00003631  __lesf2                              
00002b85  __ltdf2                              
00003631  __ltsf2                              
UNDEFED   __mpu_init                           
00001e0d  __muldf3                             
0000366d  __muldsi3                            
00002671  __mulsf3                             
00002b85  __nedf2                              
00003631  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001171  __subdf3                             
00001fcd  __subsf3                             
00002b11  __truncdfsf2                         
000039f5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00004371  _system_pre_init                     
0000434b  abort                                
000000c1  asin                                 
000000c1  asinl                                
00000425  atan                                 
00001305  atan2                                
00001305  atan2l                               
00000425  atanl                                
ffffffff  binit                                
20200494  delayTick                            
00000dcd  dmp_read_fifo                        
202004a0  enable_group1_irq                    
00004460  hw                                   
00000000  interruptVectors                     
00003b39  main                                 
20200322  more                                 
00002cb5  mpu6050_i2c_sda_unlock               
00001c01  mpu_read_fifo_stream                 
00000ba1  mpu_reset_fifo                       
00001769  mspm0_i2c_read                       
00002235  mspm0_i2c_write                      
0000189d  qsort                                
202002f0  quat                                 
00004410  reg                                  
2020031c  sensor_timestamp                     
20200320  sensors                              
0000148d  sqrt                                 
0000148d  sqrtl                                
000043e8  test                                 
20200498  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  asin                                 
000000c1  asinl                                
00000200  __STACK_SIZE                         
00000425  atan                                 
00000425  atanl                                
0000071d  SYSCFG_DL_GPIO_init                  
00000975  Read_Quad                            
00000ba1  mpu_reset_fifo                       
00000dcd  dmp_read_fifo                        
00000fc1  Task_Start                           
00001171  __aeabi_dsub                         
00001171  __subdf3                             
0000117b  __adddf3                             
0000117b  __aeabi_dadd                         
00001303  __aeabi_idiv0                        
00001305  atan2                                
00001305  atan2l                               
0000148d  sqrt                                 
0000148d  sqrtl                                
000015fd  GROUP1_IRQHandler                    
00001769  mspm0_i2c_read                       
0000189d  qsort                                
000019d1  PID_IQ_Prosc                         
00001af5  __aeabi_ddiv                         
00001af5  __divdf3                             
00001c01  mpu_read_fifo_stream                 
00001d09  Task_Motor_PID                       
00001e0d  __aeabi_dmul                         
00001e0d  __muldf3                             
00001ef1  DL_SYSCTL_configSYSPLL               
00001fcd  __aeabi_fsub                         
00001fcd  __subsf3                             
00001fd7  __addsf3                             
00001fd7  __aeabi_fadd                         
000020a5  Motor_Start                          
00002171  DL_Timer_initPWMMode                 
00002235  mspm0_i2c_write                      
000023b1  Motor_SetDuty                        
00002469  Task_Add                             
0000251d  SYSCFG_DL_initPower                  
000025cd  Motor_GetSpeed                       
00002671  __aeabi_fmul                         
00002671  __mulsf3                             
00002789  SYSCFG_DL_UART0_init                 
00002891  __aeabi_fdiv                         
00002891  __divsf3                             
00002915  SYSCFG_DL_MotorBack_init             
00002995  SYSCFG_DL_MotorFront_init            
00002a15  __TI_decompress_lzss                 
00002a91  __gedf2                              
00002a91  __gtdf2                              
00002b05  SYSCFG_DL_DMA_init                   
00002b11  __aeabi_d2f                          
00002b11  __truncdfsf2                         
00002b85  __cmpdf2                             
00002b85  __eqdf2                              
00002b85  __ledf2                              
00002b85  __ltdf2                              
00002b85  __nedf2                              
00002bed  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002c51  SYSCFG_DL_I2C_OLED_init              
00002cb5  mpu6050_i2c_sda_unlock               
00002d19  __aeabi_dcmpeq                       
00002d2d  __aeabi_dcmplt                       
00002d41  __aeabi_dcmple                       
00002d55  __aeabi_dcmpge                       
00002d69  __aeabi_dcmpgt                       
00002d7d  __aeabi_fcmpeq                       
00002d91  __aeabi_fcmplt                       
00002da5  __aeabi_fcmple                       
00002db9  __aeabi_fcmpge                       
00002dcd  __aeabi_fcmpgt                       
00002de1  Task_IdleFunction                    
00002e41  DL_I2C_fillControllerTXFIFO          
00002ea1  SYSCFG_DL_SYSCTL_init                
00002efd  SYSCFG_DL_I2C_MPU6050_init           
00002f55  Serial_Init                          
00002fad  Interrupt_Init                       
00003141  DL_DMA_initChannel                   
000031d9  SYSCFG_DL_ADC1_init                  
00003271  DL_UART_init                         
000032b9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000032fd  PID_IQ_SetParams                     
00003385  __aeabi_d2uiz                        
00003385  __fixunsdfsi                         
000033c9  DL_ADC12_setClockConfig              
00003409  __aeabi_uidiv                        
00003409  __aeabi_uidivmod                     
00003449  __aeabi_f2d                          
00003449  __extendsfdf2                        
00003541  DL_I2C_flushControllerTXFIFO         
0000357d  __aeabi_i2f                          
0000357d  __floatsisf                          
000035b9  __gesf2                              
000035b9  __gtsf2                              
000035f5  __TI_auto_init_nobinit_nopinit       
00003631  __cmpsf2                             
00003631  __eqsf2                              
00003631  __lesf2                              
00003631  __ltsf2                              
00003631  __nesf2                              
0000366d  __muldsi3                            
000036a9  __aeabi_f2iz                         
000036a9  __fixsfsi                            
00003779  SYSCFG_DL_DMA_CH_RX_init             
000037a9  SYSCFG_DL_init                       
000037d9  _IQ24toF                             
00003809  Task_Init                            
00003861  PID_IQ_Init                          
000039cd  SysTick_Increasment                  
000039f5  _c_int00_noargs                      
00003a8f  DL_I2C_setClockConfig                
00003b19  Delay                                
00003b39  main                                 
00003cc9  DL_Timer_setCaptCompUpdateMethod     
00003ce5  DL_Timer_setClockConfig              
00003fed  DL_Timer_setCaptureCompareOutCtl     
000040ad  SYSCFG_DL_DMA_CH_TX_init             
000040c5  _IQ24div                             
000040dd  _IQ24mpy                             
00004163  SysGetTick                           
00004179  __TI_zero_init_nomemset              
00004245  DL_UART_setClockConfig               
00004257  TI_memcpy_small                      
00004269  __TI_decompress_none                 
0000428d  DL_Timer_setCaptureCompareValue      
0000429d  SYSCFG_DL_SYSTICK_init               
000042ad  Sys_GetTick                          
000042b9  DL_Common_delayCycles                
00004315  SysTick_Handler                      
0000431d  __aeabi_errno_addr                   
00004325  __aeabi_memcpy                       
00004325  __aeabi_memcpy4                      
00004325  __aeabi_memcpy8                      
0000434b  abort                                
00004351  ADC0_IRQHandler                      
00004351  ADC1_IRQHandler                      
00004351  AES_IRQHandler                       
00004351  CANFD0_IRQHandler                    
00004351  DAC0_IRQHandler                      
00004351  DMA_IRQHandler                       
00004351  Default_Handler                      
00004351  GROUP0_IRQHandler                    
00004351  HardFault_Handler                    
00004351  I2C0_IRQHandler                      
00004351  I2C1_IRQHandler                      
00004351  NMI_Handler                          
00004351  PendSV_Handler                       
00004351  RTC_IRQHandler                       
00004351  SPI0_IRQHandler                      
00004351  SPI1_IRQHandler                      
00004351  SVC_Handler                          
00004351  TIMA0_IRQHandler                     
00004351  TIMA1_IRQHandler                     
00004351  TIMG0_IRQHandler                     
00004351  TIMG12_IRQHandler                    
00004351  TIMG6_IRQHandler                     
00004351  TIMG7_IRQHandler                     
00004351  TIMG8_IRQHandler                     
00004351  UART0_IRQHandler                     
00004351  UART1_IRQHandler                     
00004351  UART2_IRQHandler                     
00004351  UART3_IRQHandler                     
00004354  C$$EXIT                              
00004355  HOSTexit                             
0000435d  Reset_Handler                        
00004371  _system_pre_init                     
000043e8  test                                 
00004410  reg                                  
00004460  hw                                   
00004504  __TI_Handler_Table_Base              
00004510  __TI_Handler_Table_Limit             
00004518  __TI_CINIT_Base                      
00004528  __TI_CINIT_Limit                     
00004528  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
20200460  Motor                                
20200480  Data_MotorEncoder                    
20200488  Data_Motor_TarSpeed                  
2020048c  Data_Tracker_Offset                  
20200490  __aeabi_errno                        
20200494  delayTick                            
20200498  uwTick                               
2020049e  Flag_MPU6050_Ready                   
202004a0  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[223 symbols]
