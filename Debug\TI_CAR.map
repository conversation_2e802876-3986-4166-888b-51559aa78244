******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 15:12:35 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001529


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001d20  0001e2e0  R  X
  SRAM                  20200000   00008000  00000525  00007adb  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001d20   00001d20    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001b78   00001b78    r-x .text
  00001c38    00001c38    00000080   00000080    r-- .rodata
  00001cb8    00001cb8    00000068   00000068    r-- .cinit
20200000    20200000    00000325   00000000    rw-
  20200000    20200000    00000204   00000000    rw- .bss
  20200204    20200204    00000121   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001b78     
                  000000c0    000002b0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000370    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  000004d8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000005b4    000000cc     Motor.o (.text.Motor_Start)
                  00000680    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00000744    000000b8     Motor.o (.text.Motor_SetDirc)
                  000007fc    000000b8     Motor.o (.text.Motor_SetDuty)
                  000008b4    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000954    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000009e0    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00000a64    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00000ae8    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00000b68    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00000be8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000c64    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00000cc8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000d2c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000d8e    00000002     --HOLE-- [fill = 0]
                  00000d90    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000dec    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00000e44    00000058     Serial.o (.text.Serial_Init)
                  00000e9c    00000054     Interrupt.o (.text.Interrupt_Init)
                  00000ef0    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00000f40    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00000f8c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00000fd8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001020    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00001064    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  000010a8    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000010ea    00000002     --HOLE-- [fill = 0]
                  000010ec    00000040     Motor.o (.text.Motor_Test_TB6612)
                  0000112c    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000116c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000011a8    0000003c     libclang_rt.builtins.a : comparesf2.S.obj (.text.__gtsf2)
                  000011e4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001220    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000125a    00000002     --HOLE-- [fill = 0]
                  0000125c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00001296    00000002     --HOLE-- [fill = 0]
                  00001298    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000012d0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001304    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00001334    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00001364    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001390    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000013bc    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000013e6    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000140e    00000002     --HOLE-- [fill = 0]
                  00001410    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00001438    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00001460    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00001488    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000014b0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000014d8    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00001500    00000028     SysTick.o (.text.SysTick_Increasment)
                  00001528    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001550    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00001576    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000159c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000015c2    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000015e8    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000160c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000162c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  0000164c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000166a    00000002     --HOLE-- [fill = 0]
                  0000166c    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00001688    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000016a4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000016c0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000016dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000016f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001714    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001730    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000174c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001768    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001784    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000017a0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000017bc    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000017d8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000017f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001808    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001820    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001838    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001850    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00001868    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001880    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00001898    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000018b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000018c8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000018e0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000018f8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001910    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001928    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001940    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001958    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00001970    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00001988    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000019a0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000019b8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000019d0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000019e8    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00001a00    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00001a18    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00001a30    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00001a48    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00001a60    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001a78    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001a90    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00001aa8    00000018     main.o (.text.main)
                  00001ac0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00001ad6    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001aec    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001b02    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00001b16    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001b2a    00000002     --HOLE-- [fill = 0]
                  00001b2c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00001b40    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001b54    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001b68    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00001b7c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001b8e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001ba0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00001bb2    00000002     --HOLE-- [fill = 0]
                  00001bb4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001bc4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001bd4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001be4    00000010     Task_App.o (.text.Task_Init)
                  00001bf4    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00001c00    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001c0a    00000008     Interrupt.o (.text.SysTick_Handler)
                  00001c12    00000002     --HOLE-- [fill = 0]
                  00001c14    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001c1c    00000006     libc.a : exit.c.obj (.text:abort)
                  00001c22    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001c26    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001c2a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001c2e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001c32    00000006     --HOLE-- [fill = 0]

.cinit     0    00001cb8    00000068     
                  00001cb8    00000044     (.cinit..data.load) [load image, compression = lzss]
                  00001cfc    0000000c     (__TI_handler_table)
                  00001d08    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001d10    00000010     (__TI_cinit_table)

.rodata    0    00001c38    00000080     
                  00001c38    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001c60    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00001c78    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00001c90    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00001c9a    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00001c9c    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00001ca4    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00001cac    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00001caf    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00001cb2    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001cb4    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00001cb6    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000204     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    00000004     (.common:ExISR_Flag)

.data      0    20200204    00000121     UNINITIALIZED
                  20200204    00000044     Motor.o (.data.Motor_Back_Left)
                  20200248    00000044     Motor.o (.data.Motor_Back_Right)
                  2020028c    00000044     Motor.o (.data.Motor_Font_Left)
                  202002d0    00000044     Motor.o (.data.Motor_Font_Right)
                  20200314    00000008     Task_App.o (.data.Data_MotorEncoder)
                  2020031c    00000004     SysTick.o (.data.delayTick)
                  20200320    00000004     SysTick.o (.data.uwTick)
                  20200324    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             3394   126       0      
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         24     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         3426   318       0      
                                                              
    .\APP\Src\
       Interrupt.o                    622    0         5      
       Task_App.o                     16     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         638    0         13     
                                                              
    .\BSP\Src\
       Motor.o                        704    0         272    
       Serial.o                       292    0         512    
       PID_IQMath.o                   110    0         0      
       SysTick.o                      40     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         1146   0         792    
                                                              
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     292    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         894    0         0      
                                                              
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       extendsfdf2.S.obj              64     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         608    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      104       0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   7008   422       1317   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001d10 records: 2, size/record: 8, table size: 16
	.data: load addr=00001cb8, load size=00000044 bytes, run addr=20200204, run size=00000121 bytes, compression=lzss
	.bss: load addr=00001d08, load size=00000008 bytes, run addr=20200000, run size=00000204 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001cfc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001c23  ADC0_IRQHandler                      
00001c23  ADC1_IRQHandler                      
00001c23  AES_IRQHandler                       
00001c26  C$$EXIT                              
00001c23  CANFD0_IRQHandler                    
00001c23  DAC0_IRQHandler                      
00001c01  DL_Common_delayCycles                
00000f41  DL_DMA_initChannel                   
000015c3  DL_I2C_setClockConfig                
000004d9  DL_SYSCTL_configSYSPLL               
00000c65  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001021  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000681  DL_Timer_initPWMMode                 
00001785  DL_Timer_setCaptCompUpdateMethod     
000019d1  DL_Timer_setCaptureCompareOutCtl     
00001bc5  DL_Timer_setCaptureCompareValue      
000017a1  DL_Timer_setClockConfig              
00000fd9  DL_UART_init                         
00001b7d  DL_UART_setClockConfig               
00001c23  DMA_IRQHandler                       
20200314  Data_MotorEncoder                    
00001c23  Default_Handler                      
20200200  ExISR_Flag                           
20200324  Flag_MPU6050_Ready                   
00001c23  GROUP0_IRQHandler                    
00000371  GROUP1_IRQHandler                    
00001c27  HOSTexit                             
00001c23  HardFault_Handler                    
00001c23  I2C0_IRQHandler                      
00001c23  I2C1_IRQHandler                      
00000e9d  Interrupt_Init                       
20200204  Motor_Back_Left                      
20200248  Motor_Back_Right                     
2020028c  Motor_Font_Left                      
202002d0  Motor_Font_Right                     
000007fd  Motor_SetDuty                        
000005b5  Motor_Start                          
000010ed  Motor_Test_TB6612                    
00001c23  NMI_Handler                          
000013bd  PID_IQ_Init                          
00001065  PID_IQ_SetParams                     
00001c23  PendSV_Handler                       
00001c23  RTC_IRQHandler                       
00001c2b  Reset_Handler                        
00001c23  SPI0_IRQHandler                      
00001c23  SPI1_IRQHandler                      
00001c23  SVC_Handler                          
00001335  SYSCFG_DL_DMA_CH_RX_init             
00001a91  SYSCFG_DL_DMA_CH_TX_init             
00001bf5  SYSCFG_DL_DMA_init                   
000000c1  SYSCFG_DL_GPIO_init                  
00000ded  SYSCFG_DL_I2C_MPU6050_init           
00000cc9  SYSCFG_DL_I2C_OLED_init              
00000ae9  SYSCFG_DL_MotorBack_init             
00000b69  SYSCFG_DL_MotorFront_init            
00000d91  SYSCFG_DL_SYSCTL_init                
00001bd5  SYSCFG_DL_SYSTICK_init               
000009e1  SYSCFG_DL_UART0_init                 
00001365  SYSCFG_DL_init                       
000008b5  SYSCFG_DL_initPower                  
00000e45  Serial_Init                          
20200000  Serial_RxData                        
00001c0b  SysTick_Handler                      
00001501  SysTick_Increasment                  
00001c23  TIMA0_IRQHandler                     
00001c23  TIMA1_IRQHandler                     
00001c23  TIMG0_IRQHandler                     
00001c23  TIMG12_IRQHandler                    
00001c23  TIMG6_IRQHandler                     
00001c23  TIMG7_IRQHandler                     
00001c23  TIMG8_IRQHandler                     
00001b8f  TI_memcpy_small                      
00001be5  Task_Init                            
00001c23  UART0_IRQHandler                     
00001c23  UART1_IRQHandler                     
00001c23  UART2_IRQHandler                     
00001c23  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001d10  __TI_CINIT_Base                      
00001d20  __TI_CINIT_Limit                     
00001d20  __TI_CINIT_Warm                      
00001cfc  __TI_Handler_Table_Base              
00001d08  __TI_Handler_Table_Limit             
000011e5  __TI_auto_init_nobinit_nopinit       
00000be9  __TI_decompress_lzss                 
00001ba1  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00001aed  __TI_zero_init_nomemset              
000010a9  __aeabi_d2uiz                        
0000112d  __aeabi_f2d                          
00001299  __aeabi_f2iz                         
00000d2d  __aeabi_fcmpeq                       
00000d69  __aeabi_fcmpge                       
00000d7d  __aeabi_fcmpgt                       
00000d55  __aeabi_fcmple                       
00000d41  __aeabi_fcmplt                       
00000955  __aeabi_fmul                         
00001c15  __aeabi_memcpy                       
00001c15  __aeabi_memcpy4                      
00001c15  __aeabi_memcpy8                      
ffffffff  __binit__                            
00001221  __cmpsf2                             
00001221  __eqsf2                              
0000112d  __extendsfdf2                        
00001299  __fixsfsi                            
000010a9  __fixunsdfsi                         
000011a9  __gesf2                              
000011a9  __gtsf2                              
00001221  __lesf2                              
00001221  __ltsf2                              
UNDEFED   __mpu_init                           
0000125d  __muldsi3                            
00000955  __mulsf3                             
00001221  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001529  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00001c2f  _system_pre_init                     
00001c1d  abort                                
ffffffff  binit                                
2020031c  delayTick                            
00000000  interruptVectors                     
00001aa9  main                                 
20200320  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  SYSCFG_DL_GPIO_init                  
00000200  __STACK_SIZE                         
00000371  GROUP1_IRQHandler                    
000004d9  DL_SYSCTL_configSYSPLL               
000005b5  Motor_Start                          
00000681  DL_Timer_initPWMMode                 
000007fd  Motor_SetDuty                        
000008b5  SYSCFG_DL_initPower                  
00000955  __aeabi_fmul                         
00000955  __mulsf3                             
000009e1  SYSCFG_DL_UART0_init                 
00000ae9  SYSCFG_DL_MotorBack_init             
00000b69  SYSCFG_DL_MotorFront_init            
00000be9  __TI_decompress_lzss                 
00000c65  DL_SYSCTL_setHFCLKSourceHFXTParams   
00000cc9  SYSCFG_DL_I2C_OLED_init              
00000d2d  __aeabi_fcmpeq                       
00000d41  __aeabi_fcmplt                       
00000d55  __aeabi_fcmple                       
00000d69  __aeabi_fcmpge                       
00000d7d  __aeabi_fcmpgt                       
00000d91  SYSCFG_DL_SYSCTL_init                
00000ded  SYSCFG_DL_I2C_MPU6050_init           
00000e45  Serial_Init                          
00000e9d  Interrupt_Init                       
00000f41  DL_DMA_initChannel                   
00000fd9  DL_UART_init                         
00001021  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001065  PID_IQ_SetParams                     
000010a9  __aeabi_d2uiz                        
000010a9  __fixunsdfsi                         
000010ed  Motor_Test_TB6612                    
0000112d  __aeabi_f2d                          
0000112d  __extendsfdf2                        
000011a9  __gesf2                              
000011a9  __gtsf2                              
000011e5  __TI_auto_init_nobinit_nopinit       
00001221  __cmpsf2                             
00001221  __eqsf2                              
00001221  __lesf2                              
00001221  __ltsf2                              
00001221  __nesf2                              
0000125d  __muldsi3                            
00001299  __aeabi_f2iz                         
00001299  __fixsfsi                            
00001335  SYSCFG_DL_DMA_CH_RX_init             
00001365  SYSCFG_DL_init                       
000013bd  PID_IQ_Init                          
00001501  SysTick_Increasment                  
00001529  _c_int00_noargs                      
000015c3  DL_I2C_setClockConfig                
00001785  DL_Timer_setCaptCompUpdateMethod     
000017a1  DL_Timer_setClockConfig              
000019d1  DL_Timer_setCaptureCompareOutCtl     
00001a91  SYSCFG_DL_DMA_CH_TX_init             
00001aa9  main                                 
00001aed  __TI_zero_init_nomemset              
00001b7d  DL_UART_setClockConfig               
00001b8f  TI_memcpy_small                      
00001ba1  __TI_decompress_none                 
00001bc5  DL_Timer_setCaptureCompareValue      
00001bd5  SYSCFG_DL_SYSTICK_init               
00001be5  Task_Init                            
00001bf5  SYSCFG_DL_DMA_init                   
00001c01  DL_Common_delayCycles                
00001c0b  SysTick_Handler                      
00001c15  __aeabi_memcpy                       
00001c15  __aeabi_memcpy4                      
00001c15  __aeabi_memcpy8                      
00001c1d  abort                                
00001c23  ADC0_IRQHandler                      
00001c23  ADC1_IRQHandler                      
00001c23  AES_IRQHandler                       
00001c23  CANFD0_IRQHandler                    
00001c23  DAC0_IRQHandler                      
00001c23  DMA_IRQHandler                       
00001c23  Default_Handler                      
00001c23  GROUP0_IRQHandler                    
00001c23  HardFault_Handler                    
00001c23  I2C0_IRQHandler                      
00001c23  I2C1_IRQHandler                      
00001c23  NMI_Handler                          
00001c23  PendSV_Handler                       
00001c23  RTC_IRQHandler                       
00001c23  SPI0_IRQHandler                      
00001c23  SPI1_IRQHandler                      
00001c23  SVC_Handler                          
00001c23  TIMA0_IRQHandler                     
00001c23  TIMA1_IRQHandler                     
00001c23  TIMG0_IRQHandler                     
00001c23  TIMG12_IRQHandler                    
00001c23  TIMG6_IRQHandler                     
00001c23  TIMG7_IRQHandler                     
00001c23  TIMG8_IRQHandler                     
00001c23  UART0_IRQHandler                     
00001c23  UART1_IRQHandler                     
00001c23  UART2_IRQHandler                     
00001c23  UART3_IRQHandler                     
00001c26  C$$EXIT                              
00001c27  HOSTexit                             
00001c2b  Reset_Handler                        
00001c2f  _system_pre_init                     
00001cfc  __TI_Handler_Table_Base              
00001d08  __TI_Handler_Table_Limit             
00001d10  __TI_CINIT_Base                      
00001d20  __TI_CINIT_Limit                     
00001d20  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200200  ExISR_Flag                           
20200204  Motor_Back_Left                      
20200248  Motor_Back_Right                     
2020028c  Motor_Font_Left                      
202002d0  Motor_Font_Right                     
20200314  Data_MotorEncoder                    
2020031c  delayTick                            
20200320  uwTick                               
20200324  Flag_MPU6050_Ready                   
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[141 symbols]
