/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.0+3721"}
 */

/**
 * Import the modules used in this configuration.
 */
const Board         = scripting.addModule("/ti/driverlib/Board");
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const GPIO4         = GPIO.addInstance();
const GPIO5         = GPIO.addInstance();
const GPIO6         = GPIO.addInstance();
const GPIO7         = GPIO.addInstance();
const GPIO8         = GPIO.addInstance();
const GPIO9         = GPIO.addInstance();
const GPIO10        = GPIO.addInstance();
const I2C           = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1          = I2C.addInstance();
const I2C2          = I2C.addInstance();
const MATHACL       = scripting.addModule("/ti/driverlib/MATHACL");
const PWM           = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1          = PWM.addInstance();
const PWM2          = PWM.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK       = scripting.addModule("/ti/driverlib/SYSTICK");
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const pinFunction4     = system.clockTree["HFXT"];
pinFunction4.inputFreq = 40;
pinFunction4.enable    = true;


GPIO1.$name                          = "LED";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "14";
GPIO1.associatedPins[0].$name        = "Board";
GPIO1.associatedPins[1].initialValue = "SET";
GPIO1.associatedPins[1].ioStructure  = "SD";
GPIO1.associatedPins[1].$name        = "RED";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "23";
GPIO1.associatedPins[2].$name        = "BLUE";
GPIO1.associatedPins[2].ioStructure  = "SD";
GPIO1.associatedPins[2].initialValue = "SET";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "22";

GPIO2.$name                              = "KEY";
GPIO2.associatedPins.create(3);
GPIO2.associatedPins[0].$name            = "S2";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPort     = "PORTB";
GPIO2.associatedPins[0].assignedPin      = "21";
GPIO2.associatedPins[1].$name            = "K1";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].internalResistor = "PULL_UP";
GPIO2.associatedPins[1].ioStructure      = "SD";
GPIO2.associatedPins[1].assignedPort     = "PORTB";
GPIO2.associatedPins[1].assignedPin      = "19";
GPIO2.associatedPins[2].$name            = "K2";
GPIO2.associatedPins[2].direction        = "INPUT";
GPIO2.associatedPins[2].ioStructure      = "SD";
GPIO2.associatedPins[2].internalResistor = "PULL_UP";
GPIO2.associatedPins[2].assignedPin      = "20";
GPIO2.associatedPins[2].assignedPort     = "PORTB";

GPIO3.port                                = "PORTA";
GPIO3.$name                               = "SPD_READER_A";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].direction         = "INPUT";
GPIO3.associatedPins[0].interruptEn       = true;
GPIO3.associatedPins[0].interruptPriority = "1";
GPIO3.associatedPins[0].$name             = "FONT_LEFT_A";
GPIO3.associatedPins[0].assignedPin       = "27";
GPIO3.associatedPins[0].polarity          = "RISE";
GPIO3.associatedPins[0].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[1].direction         = "INPUT";
GPIO3.associatedPins[1].interruptEn       = true;
GPIO3.associatedPins[1].interruptPriority = "1";
GPIO3.associatedPins[1].$name             = "FONT_RIGHT_A";
GPIO3.associatedPins[1].assignedPin       = "26";
GPIO3.associatedPins[1].polarity          = "RISE";
GPIO3.associatedPins[1].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[2].direction         = "INPUT";
GPIO3.associatedPins[2].interruptEn       = true;
GPIO3.associatedPins[2].interruptPriority = "1";
GPIO3.associatedPins[2].$name             = "BACK_LEFT_A";
GPIO3.associatedPins[2].assignedPin       = "25";
GPIO3.associatedPins[2].polarity          = "RISE";
GPIO3.associatedPins[2].internalResistor  = "PULL_DOWN";
GPIO3.associatedPins[3].direction         = "INPUT";
GPIO3.associatedPins[3].interruptEn       = true;
GPIO3.associatedPins[3].interruptPriority = "1";
GPIO3.associatedPins[3].$name             = "BACK_RIGHT_A";
GPIO3.associatedPins[3].assignedPin       = "24";
GPIO3.associatedPins[3].polarity          = "RISE";
GPIO3.associatedPins[3].internalResistor  = "PULL_DOWN";

GPIO4.$name                              = "SPD_READER_B";
GPIO4.port                               = "PORTB";
GPIO4.associatedPins.create(4);
GPIO4.associatedPins[0].$name            = "FONT_LEFT_B";
GPIO4.associatedPins[0].direction        = "INPUT";
GPIO4.associatedPins[0].ioStructure      = "SD";
GPIO4.associatedPins[0].assignedPin      = "10";
GPIO4.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[1].$name            = "FONT_RIGHT_B";
GPIO4.associatedPins[1].direction        = "INPUT";
GPIO4.associatedPins[1].ioStructure      = "SD";
GPIO4.associatedPins[1].assignedPin      = "11";
GPIO4.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[2].$name            = "BACK_LEFT_B";
GPIO4.associatedPins[2].direction        = "INPUT";
GPIO4.associatedPins[2].ioStructure      = "SD";
GPIO4.associatedPins[2].assignedPin      = "12";
GPIO4.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO4.associatedPins[3].$name            = "BACK_RIGHT_B";
GPIO4.associatedPins[3].direction        = "INPUT";
GPIO4.associatedPins[3].ioStructure      = "SD";
GPIO4.associatedPins[3].assignedPin      = "13";
GPIO4.associatedPins[3].internalResistor = "PULL_DOWN";

GPIO5.$name                          = "DIRC_CTRL";
GPIO5.port                           = "PORTB";
GPIO5.associatedPins.create(4);
GPIO5.associatedPins[0].$name        = "FONT_LEFT";
GPIO5.associatedPins[0].assignedPin  = "6";
GPIO5.associatedPins[0].ioStructure  = "SD";
GPIO5.associatedPins[0].initialValue = "SET";
GPIO5.associatedPins[1].$name        = "FONT_RIGHT";
GPIO5.associatedPins[1].assignedPin  = "7";
GPIO5.associatedPins[1].ioStructure  = "SD";
GPIO5.associatedPins[1].initialValue = "SET";
GPIO5.associatedPins[2].$name        = "BACK_LEFT";
GPIO5.associatedPins[2].assignedPin  = "8";
GPIO5.associatedPins[2].ioStructure  = "SD";
GPIO5.associatedPins[2].initialValue = "SET";
GPIO5.associatedPins[3].$name        = "BACK_RIGHT";
GPIO5.associatedPins[3].assignedPin  = "9";
GPIO5.associatedPins[3].ioStructure  = "SD";
GPIO5.associatedPins[3].initialValue = "SET";

GPIO6.$name                              = "Tracker";
GPIO6.port                               = "PORTB";
GPIO6.associatedPins.create(8);
GPIO6.associatedPins[0].$name            = "_1";
GPIO6.associatedPins[0].direction        = "INPUT";
GPIO6.associatedPins[0].ioStructure      = "SD";
GPIO6.associatedPins[0].assignedPin      = "0";
GPIO6.associatedPins[0].internalResistor = "PULL_UP";
GPIO6.associatedPins[1].$name            = "_2";
GPIO6.associatedPins[1].direction        = "INPUT";
GPIO6.associatedPins[1].ioStructure      = "SD";
GPIO6.associatedPins[1].assignedPin      = "1";
GPIO6.associatedPins[1].internalResistor = "PULL_UP";
GPIO6.associatedPins[2].$name            = "_3";
GPIO6.associatedPins[2].direction        = "INPUT";
GPIO6.associatedPins[2].ioStructure      = "SD";
GPIO6.associatedPins[2].assignedPin      = "4";
GPIO6.associatedPins[2].internalResistor = "PULL_UP";
GPIO6.associatedPins[3].$name            = "_4";
GPIO6.associatedPins[3].direction        = "INPUT";
GPIO6.associatedPins[3].ioStructure      = "SD";
GPIO6.associatedPins[3].internalResistor = "PULL_UP";
GPIO6.associatedPins[3].assignedPin      = "5";
GPIO6.associatedPins[4].$name            = "_5";
GPIO6.associatedPins[4].direction        = "INPUT";
GPIO6.associatedPins[4].ioStructure      = "SD";
GPIO6.associatedPins[4].internalResistor = "PULL_UP";
GPIO6.associatedPins[4].assignedPin      = "15";
GPIO6.associatedPins[5].$name            = "_6";
GPIO6.associatedPins[5].direction        = "INPUT";
GPIO6.associatedPins[5].ioStructure      = "SD";
GPIO6.associatedPins[5].internalResistor = "PULL_UP";
GPIO6.associatedPins[5].assignedPin      = "16";
GPIO6.associatedPins[6].$name            = "_7";
GPIO6.associatedPins[6].direction        = "INPUT";
GPIO6.associatedPins[6].ioStructure      = "SD";
GPIO6.associatedPins[6].internalResistor = "PULL_UP";
GPIO6.associatedPins[6].assignedPin      = "17";
GPIO6.associatedPins[7].$name            = "_8";
GPIO6.associatedPins[7].direction        = "INPUT";
GPIO6.associatedPins[7].assignedPin      = "18";
GPIO6.associatedPins[7].ioStructure      = "SD";
GPIO6.associatedPins[7].internalResistor = "PULL_UP";

GPIO7.$name                          = "BUZZ";
GPIO7.associatedPins[0].assignedPort = "PORTA";
GPIO7.associatedPins[0].ioStructure  = "SD";
GPIO7.associatedPins[0].assignedPin  = "3";
GPIO7.associatedPins[0].$name        = "Periph";

GPIO8.$name                               = "GPIO_MPU6050";
GPIO8.port                                = "PORTA";
GPIO8.portSegment                         = "Upper";
GPIO8.associatedPins[0].$name             = "PIN_INT";
GPIO8.associatedPins[0].direction         = "INPUT";
GPIO8.associatedPins[0].assignedPin       = "30";
GPIO8.associatedPins[0].interruptEn       = true;
GPIO8.associatedPins[0].internalResistor  = "PULL_UP";
GPIO8.associatedPins[0].interruptPriority = "1";
GPIO8.associatedPins[0].polarity          = "FALL";

GPIO9.$name                          = "DIRC_CTRL_IN2";
GPIO9.port                           = "PORTA";
GPIO9.associatedPins.create(4);
GPIO9.associatedPins[0].$name        = "FONT_LEFT_IN2";
GPIO9.associatedPins[0].initialValue = "SET";
GPIO9.associatedPins[0].ioStructure  = "SD";
GPIO9.associatedPins[0].assignedPin  = "15";
GPIO9.associatedPins[1].$name        = "FONT_RIGHT_IN2";
GPIO9.associatedPins[1].initialValue = "SET";
GPIO9.associatedPins[1].ioStructure  = "SD";
GPIO9.associatedPins[1].assignedPin  = "16";
GPIO9.associatedPins[1].pin.$assign  = "PA16";
GPIO9.associatedPins[2].$name        = "BACK_LEFT_IN2";
GPIO9.associatedPins[2].initialValue = "SET";
GPIO9.associatedPins[2].ioStructure  = "SD";
GPIO9.associatedPins[2].assignedPin  = "21";
GPIO9.associatedPins[3].$name        = "BACK_RIGHT_IN2";
GPIO9.associatedPins[3].initialValue = "SET";
GPIO9.associatedPins[3].ioStructure  = "SD";
GPIO9.associatedPins[3].assignedPin  = "22";

GPIO10.$name                          = "TB6612_STBY";
GPIO10.port                           = "PORTA";
GPIO10.associatedPins[0].$name        = "STBY";
GPIO10.associatedPins[0].initialValue = "SET";
GPIO10.associatedPins[0].ioStructure  = "SD";
GPIO10.associatedPins[0].assignedPin  = "23";

I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.$name                             = "I2C_MPU6050";
I2C1.peripheral.$assign                = "I2C0";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

I2C2.basicEnableController             = true;
I2C2.basicControllerStandardBusSpeed   = "Fast";
I2C2.intController                     = ["ARBITRATION_LOST","NACK","RXFIFO_TRIGGER","RX_DONE","TX_DONE"];
I2C2.$name                             = "I2C_OLED";
I2C2.peripheral.$assign                = "I2C1";
I2C2.peripheral.sdaPin.$assign         = "PB3";
I2C2.peripheral.sclPin.$assign         = "PB2";
I2C2.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
I2C2.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric9";

PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.$name                              = "MotorFront";
PWM1.timerStartTimer                    = true;
PWM1.timerCount                         = 100;
PWM1.clockPrescale                      = 40;
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 1;
PWM1.PWM_CHANNEL_0.ccValue              = 1;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.dutyCycle            = 2;
PWM1.PWM_CHANNEL_1.ccValue              = 2;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

PWM2.$name                              = "MotorBack";
PWM2.clockPrescale                      = 40;
PWM2.timerCount                         = 100;
PWM2.timerStartTimer                    = true;
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.peripheral.$assign                 = "TIMG8";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_0.ccValue              = 3;
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.PWM_CHANNEL_1.ccValue              = 4;
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.interruptEnable   = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 80000;

UART1.targetBaudRate                   = 115200;
UART1.enableFIFO                       = true;
UART1.rxFifoThreshold                  = "DL_UART_RX_FIFO_LEVEL_3_4_FULL";
UART1.rxTimeoutValue                   = 15;
UART1.$name                            = "UART0";
UART1.interruptPriority                = "1";
UART1.enabledInterrupts                = ["DMA_DONE_RX","DMA_DONE_TX","EOT_DONE","RX_TIMEOUT_ERROR"];
UART1.enabledDMARXTriggers             = "DL_UART_DMA_INTERRUPT_RX_TIMEOUT";
UART1.enabledDMATXTriggers             = "DL_UART_DMA_INTERRUPT_TX";
UART1.peripheral.rxPin.$assign         = "PA11";
UART1.peripheral.txPin.$assign         = "PA10";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
UART1.DMA_CHANNEL_RX.enableInterrupt   = true;
UART1.DMA_CHANNEL_RX.srcLength         = "BYTE";
UART1.DMA_CHANNEL_RX.dstLength         = "BYTE";
UART1.DMA_CHANNEL_RX.$name             = "DMA_CH_RX";
UART1.DMA_CHANNEL_RX.transferMode      = "FULL_CH_REPEAT_SINGLE";
UART1.DMA_CHANNEL_RX.addressMode       = "f2b";
UART1.DMA_CHANNEL_RX.interruptPriority = "1";
UART1.DMA_CHANNEL_TX.addressMode       = "b2f";
UART1.DMA_CHANNEL_TX.srcLength         = "BYTE";
UART1.DMA_CHANNEL_TX.dstLength         = "BYTE";
UART1.DMA_CHANNEL_TX.$name             = "DMA_CH_TX";

ProjectConfig.genLibIQ        = true;
ProjectConfig.genLibIQVersion = "MATHACL";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB14";
GPIO1.associatedPins[1].pin.$suggestSolution       = "PB23";
GPIO1.associatedPins[2].pin.$suggestSolution       = "PB22";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PB21";
GPIO2.associatedPins[1].pin.$suggestSolution       = "PB19";
GPIO2.associatedPins[2].pin.$suggestSolution       = "PB20";
GPIO3.associatedPins[0].pin.$suggestSolution       = "PA27";
GPIO3.associatedPins[1].pin.$suggestSolution       = "PA26";
GPIO3.associatedPins[2].pin.$suggestSolution       = "PA25";
GPIO3.associatedPins[3].pin.$suggestSolution       = "PA24";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PB10";
GPIO4.associatedPins[1].pin.$suggestSolution       = "PB11";
GPIO4.associatedPins[2].pin.$suggestSolution       = "PB12";
GPIO4.associatedPins[3].pin.$suggestSolution       = "PB13";
GPIO5.associatedPins[0].pin.$suggestSolution       = "PB6";
GPIO5.associatedPins[1].pin.$suggestSolution       = "PB7";
GPIO5.associatedPins[2].pin.$suggestSolution       = "PB8";
GPIO5.associatedPins[3].pin.$suggestSolution       = "PB9";
GPIO6.associatedPins[0].pin.$suggestSolution       = "PB0";
GPIO6.associatedPins[1].pin.$suggestSolution       = "PB1";
GPIO6.associatedPins[2].pin.$suggestSolution       = "PB4";
GPIO6.associatedPins[3].pin.$suggestSolution       = "PB5";
GPIO6.associatedPins[4].pin.$suggestSolution       = "PB15";
GPIO6.associatedPins[5].pin.$suggestSolution       = "PB16";
GPIO6.associatedPins[6].pin.$suggestSolution       = "PB17";
GPIO6.associatedPins[7].pin.$suggestSolution       = "PB18";
GPIO7.associatedPins[0].pin.$suggestSolution       = "PA3";
GPIO8.associatedPins[0].pin.$suggestSolution       = "PA30";
GPIO9.associatedPins[0].pin.$suggestSolution       = "PA15";
GPIO9.associatedPins[2].pin.$suggestSolution       = "PA21";
GPIO9.associatedPins[3].pin.$suggestSolution       = "PA22";
GPIO10.associatedPins[0].pin.$suggestSolution      = "PA23";
I2C1.peripheral.sdaPin.$suggestSolution            = "PA28";
I2C1.peripheral.sclPin.$suggestSolution            = "PA31";
PWM1.peripheral.ccp0Pin.$suggestSolution           = "PA12";
PWM1.peripheral.ccp1Pin.$suggestSolution           = "PA13";
PWM2.peripheral.ccp0Pin.$suggestSolution           = "PA1";
PWM2.peripheral.ccp1Pin.$suggestSolution           = "PA0";
UART1.peripheral.$suggestSolution                  = "UART0";
UART1.DMA_CHANNEL_RX.peripheral.$suggestSolution   = "DMA_CH1";
UART1.DMA_CHANNEL_TX.peripheral.$suggestSolution   = "DMA_CH0";
