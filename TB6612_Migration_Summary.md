# TB6612电机驱动迁移总结

## 概述
已成功将原有的A4950电机驱动修改为TB6612电机驱动，保持了原有的函数接口和变量名不变，确保代码兼容性。

## 完成的修改

### 1. 硬件连接指南 (motor.md)
- ✅ 更新了完整的硬件连接表格
- ✅ 添加了TB6612引脚分配说明
- ✅ 提供了电源连接指导
- ✅ 说明了TB6612控制逻辑

### 2. 电机驱动库修改

#### BSP/Inc/Motor.h
- ✅ 更新电机结构体，添加IN2引脚支持
- ✅ 保持原有函数名和变量名不变
- ✅ 添加TB6612专用函数声明

#### BSP/Src/Motor.c  
- ✅ 实现TB6612的IN1/IN2控制逻辑
- ✅ 添加STBY使能控制
- ✅ 更新电机实例配置
- ✅ 保持原有接口兼容性

### 3. 文档更新 (README.md)
- ✅ 添加TB6612引脚分配表
- ✅ 提供使用说明和示例代码
- ✅ 说明控制逻辑和注意事项

## 引脚分配总结

### PWM控制(调速)
| 电机 | MCU引脚 | TB6612引脚 |
|------|---------|------------|
| 左前轮 | PA12 | PWMA |
| 右前轮 | PA13 | PWMB |
| 左后轮 | PA1 | PWMC |
| 右后轮 | PA0 | PWMD |

### 方向控制(IN1/IN2)
| 电机 | IN1引脚 | IN2引脚 | TB6612引脚 |
|------|---------|---------|------------|
| 左前轮 | PB6 | PA15 | AIN1/AIN2 |
| 右前轮 | PB7 | PA16 | BIN1/BIN2 |
| 左后轮 | PB8 | PA21 | CIN1/CIN2 |
| 右后轮 | PB9 | PA22 | DIN1/DIN2 |

### 使能控制
| 功能 | MCU引脚 | TB6612引脚 |
|------|---------|------------|
| STBY | PA23 | STBY |

## 控制逻辑

### TB6612控制方式
```
IN1  IN2  PWM   电机状态
0    0    X     停止(制动)
0    1    PWM   正转(调速)
1    0    PWM   反转(调速)  
1    1    X     停止(制动)
STBY=0          所有电机停止
```

### 软件接口(保持不变)
```c
// 原有接口完全兼容
Motor_SetDuty(&Motor_Font_Left, 50.0f);   // 正转50%
Motor_SetDuty(&Motor_Font_Right, -30.0f); // 反转30%
Motor_SetDuty(&Motor_Back_Left, 0.0f);    // 停止制动

// 新增TB6612专用接口
Motor_Enable(true);   // 使能TB6612
Motor_Test_TB6612();  // 测试函数
```

## 使用说明

### 1. 硬件连线
按照motor.md中的连接表格进行连线，确保：
- 逻辑电源VCC接3.3V
- 电机电源VM接6-15V
- 所有地线正确连接

### 2. 软件使用
代码无需修改，原有的电机控制代码可直接使用：
- `Motor_Start()` - 初始化电机系统
- `Motor_SetDuty()` - 设置电机速度和方向
- `Motor_GetSpeed()` - 获取电机速度反馈

### 3. 测试验证
可使用新增的测试函数验证驱动：
```c
Motor_Test_TB6612(); // 所有电机30%正转测试
```

## 注意事项

1. **电源要求**: TB6612需要独立的电机电源(6-15V)
2. **电流限制**: 确保电源能提供足够电流
3. **散热**: 大电流工作时注意TB6612散热
4. **兼容性**: 保持了原有代码完全兼容

## 完成状态

- ✅ 硬件连接指南完成
- ✅ 驱动库修改完成  
- ✅ 文档更新完成
- ✅ 接口兼容性保证
- ✅ 测试函数添加

所有修改已完成，可以直接进行硬件连线和测试。
