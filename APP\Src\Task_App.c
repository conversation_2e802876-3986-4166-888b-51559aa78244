/**
 * @file Task_App.c
 * <AUTHOR> name void Task_OLED(void *para)
 * @brief 任务实现层
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Task_App.h"

#define INDEX 0.2f //转向调试系数
#define DIS_INTERVAL _IQ(0.015f)
/*Flag*/
extern bool Flag_MPU6050_Ready; //MPU6050是否准备好

/*Data Normal*/
extern uint16_t Data_Gyro[3]; // 陀螺仪原始数据[X轴, Y轴, Z轴] - 角速度传感器输出值
extern uint16_t Data_Accel[3]; // 加速度计原始数据[X轴, Y轴, Z轴] - 线性加速度传感器输出值
extern float Data_Pitch; // 俯仰角(度) - 绕X轴旋转角度，前后倾斜
extern float Data_Roll; // 横滚角(度) - 绕Y轴旋转角度，左右倾斜
extern float Data_Yaw; // 偏航角(度) - 绕Z轴旋转角度，水平转向

/*Data Motor*/
_iq Data_Motor_TarSpeed = _IQ(30); //目标基础速度
int16_t Data_MotorEncoder[4] = {0}; //四个电机的编码值 左前 右前 左后 右后
MOTOR_Def_t *Motor[4] = {&Motor_Font_Left, &Motor_Font_Right, &Motor_Back_Left, &Motor_Back_Right}; //电机实例

/*Data Tracker*/
uint8_t Data_Tracker_Input[8] = {TRACK_OFF}; //循迹模块的输入值
_iq Data_Tracker_Offset = _IQ(0); //循迹偏差
PID_IQ_Def_t Data_Tracker_PID; //转向环PID


// 灰度传感器模拟值、数字值、归一化值
unsigned short Gray_Anolog[8] = {0};
unsigned short Gray_Normal[8] = {0};
unsigned char Gray_Digtal = 0;
No_MCU_Sensor GraySensor;


/*Test*/
bool Flag_LED = false;

void Task_Key(void *para);
void Task_LED(void *para);
void Task_Motor_PID(void *para);
void Task_Serial(void *para);
void Task_OLED(void *para);
void Task_Tracker(void *para);
void Task_GraySensor(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    Serial_Init(); //初始化串口
    // OLED_Init(); //OLED初始化
    // MPU6050_Init(); //MPU6050初始化

     // 新增：初始化灰度传感器
    No_MCU_Ganv_Sensor_Init_Frist(&GraySensor); // 首次初始化
    // 使用预设的黑白阈值初始化（来自gpio_toggle_output.c）
    unsigned short white[8] = {1800,1800,1800,1800,1800,1800,1800,1800};
    unsigned short black[8] = {300,300,300,300,300,300,300,300};
    No_MCU_Ganv_Sensor_Init(&GraySensor, white, black);

    Interrupt_Init(); //中断初始化

   //  Task_Add("Motor", Task_Motor_PID, 50, NULL, 0);
     //Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
    // Task_Add("Key", Task_Key, 20, NULL, 5);
    // Task_Add("Serial", Task_Serial, 50, NULL, 2);
    Task_Add("EncoderDebug", Task_Encoder_Debug, 2000, NULL, 6); //编码器调试输出任务，2秒一次
    // Task_Add("LED", Task_LED, 100, NULL, 3);
    // Task_Add("OLED", Task_OLED, 50, NULL, 4);

    //Task_Add("GraySensor", Task_GraySensor, 10, NULL, 1);
    Task_Add("SerialTest", Task_Serial_Test, 1000, NULL, 7); //串口测试任务，1秒一次
}

//空闲任务函数
void Task_IdleFunction(void)
{
    if (Flag_MPU6050_Ready == true && enable_group1_irq) //检测到标志位后读取
    {
        Flag_MPU6050_Ready = false;
        Read_Quad();
    }
    if (!enable_group1_irq)
    {
        static uint16_t CNT = 0;
        if (++CNT == 5000)
        {
            CNT = 0;
            Read_Quad();
        }
    }
}

//OLED显示 50ms
void Task_OLED(void *para)
{
    // OLED_Printf(0, 16 * 0, 16, "Pitch:%3.2f ", Data_Pitch);
    // OLED_Printf(0, 16 * 1, 16, "Roll:%3.2f ", Data_Roll);
    // OLED_Printf(0, 16 * 2, 16, "Yaw:%3.2f ", Data_Yaw);
    // OLED_Printf(0, 16 * 3, 16, "SysTick:%u ", (uint16_t)(uwTick / 1000));
    OLED_Printf(0, 16 * 0, 8, "[1]:%4.2f  P:%4.1f ", Motor[0]->Motor_PID_Instance.Acutal_Now, Data_Pitch);
    OLED_Printf(0, 16 * 1, 8, "[2]:%4.2f  R:%4.1f ", Motor[1]->Motor_PID_Instance.Acutal_Now, Data_Roll);
    OLED_Printf(0, 16 * 2, 8, "[3]:%4.2f  Y:%4.1f ", Motor[2]->Motor_PID_Instance.Acutal_Now, Data_Yaw);
    OLED_Printf(0, 16 * 3, 8, "[4]:%4.2f  S:%us", Motor[3]->Motor_PID_Instance.Acutal_Now, (uint16_t)(uwTick / 1000));
}

//按键 20ms
void Task_Key(void *para)
{
    static uint8_t Key_Old = 0;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;
    Key_Old = Key_Temp;

    if (Key_Val)
    {
        Flag_LED ^= 1;
    }
}

//LED 测试函数 100ms
void Task_LED(void *para)
{
    if (Flag_LED == false)
    {
        LED_BOARD_OFF();
    }
    else
    {
        LED_BOARD_ON();
    }
}

//电机PID调控 50ms
void Task_Motor_PID(void *para)
{
    //获取电机速度
    for (uint8_t i = 0; i < 4; i++)
    {
        Motor_GetSpeed(Motor[i], 50);
    }

    //差速转向控制 - 偏差乘以转向系数
    _iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));

    // 左轮：偏右时减速，偏左时加速
    _iq Left_Speed = Data_Motor_TarSpeed - Steering_Adjustment;
    // 右轮：偏右时加速，偏左时减速
    _iq Right_Speed = Data_Motor_TarSpeed + Steering_Adjustment;

    // 设置目标速度
    Motor_Font_Left.Motor_PID_Instance.Target = Left_Speed;
    Motor_Back_Left.Motor_PID_Instance.Target = Left_Speed;
    Motor_Font_Right.Motor_PID_Instance.Target = Right_Speed;
    Motor_Back_Right.Motor_PID_Instance.Target = Right_Speed;

    //PID 计算
    for (uint8_t i = 0; i < 4; i++)
    {
        PID_IQ_Prosc(&Motor[i]->Motor_PID_Instance);
    }

    // 设置电机PWM输出
    for (uint8_t i = 0; i < 4; i++)
    {
        float output = _IQtoF(Motor[i]->Motor_PID_Instance.Out);
        Motor_SetDuty(Motor[i], output);
    }
}

//灰度传感器读取、计算偏差 20ms
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); //滤波系数
    _iq Temp = 0;

     bool res = false;
    if (GraySensor.ok) // 传感器初始化完成
    {
        // 将灰度数字值转换为循迹输入（8位）
        for (uint8_t i = 0; i < 8; i++)
        {
            Data_Tracker_Input[i] = (Gray_Digtal >> i) & 0x01; // 每位对应一个传感器
        }
        // 计算偏差（复用原有加权算法）
        _iq pos_sum = _IQ(0);
        uint8_t cnt = 0;
        for (uint8_t i = 0; i < 8; i++)
        {
            if (Data_Tracker_Input[i] == TRACK_ON)
            {
                _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INTERVAL);
                pos_sum += sensor_pos;
                cnt++;
            }
        }
        if (cnt > 0)
        {
            Temp = _IQdiv(pos_sum, _IQ(cnt));
            res = true;
        }
    }

    // 原有滤波逻辑不变
    if (res == true)
    {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}

//发送显示波形图 50ms
void Task_Serial(void *para)
{
    uint8_t Motor_Param = 0;
    MyPrintf_DMA("%.2f,%.2f,%.2f\r\n",
                 _IQtoF(Motor[Motor_Param]->Motor_PID_Instance.Acutal_Now),
                 _IQtoF(Motor[Motor_Param]->Motor_PID_Instance.Target),
                 _IQtoF(Motor[Motor_Param]->Motor_PID_Instance.Out));
}

//编码器调试输出 100ms
void Task_Encoder_Debug(void *para)
{
    MyPrintf_DMA("ENC: FL=%d, FR=%d, BL=%d, BR=%d\r\n",
                 Data_MotorEncoder[0], //左前轮编码器
                 Data_MotorEncoder[1], //右前轮编码器
                 Data_MotorEncoder[2], //左后轮编码器
                 Data_MotorEncoder[3]); //右后轮编码器
}

/**
 * @brief 灰度传感器数据读取任务（20ms）
 * 读取模拟值、数字值、归一化值并更新到全局变量
 */
void Task_GraySensor(void *para)
{
    // 调用传感器处理函数（无时基版本，与gpio_toggle_output.c逻辑一致）
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);

    // 获取数字值（二值化结果）
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);

    // 获取模拟值（原始ADC值）
    Get_Anolog_Value(&GraySensor, Gray_Anolog);

    // 获取归一化值（0~4095，基于12位ADC）
    Get_Normalize_For_User(&GraySensor, Gray_Normal);

    // 调试输出 - 每100次输出一次（即1秒一次）
    static uint16_t debug_cnt = 0;
    if(++debug_cnt >= 100) {
        debug_cnt = 0;
        Debug_GraySensor_Output();
    }
}

/**
 * @brief 灰度传感器调试输出函数
 * 输出8路传感器的详细信息：数字值、模拟值、归一化值
 * 电平逻辑：0=黑线(低电平)，1=白色(高电平)
 */
void Debug_GraySensor_Output(void)
{
    if(!GraySensor.ok) {
        MyPrintf_DMA("GraySensor not ready!\r\n");
        return;
    }

    MyPrintf_DMA("=== Gray Sensor Debug ===\r\n");

    // 输出数字值（二进制格式，0=黑线，1=白色）
    MyPrintf_DMA("Digital: 0x%02X [", Gray_Digtal);
    for(int i = 7; i >= 0; i--) {
        MyPrintf_DMA("%d", (Gray_Digtal >> i) & 1);
    }
    MyPrintf_DMA("]\r\n");

    // 输出传感器编号和对应状态
    MyPrintf_DMA("Sensor : ");
    for(int i = 0; i < 8; i++) {
        MyPrintf_DMA(" %d  ", i);
    }
    MyPrintf_DMA("\r\n");

    // 输出原始ADC值
    MyPrintf_DMA("ADC    : ");
    for(int i = 0; i < 8; i++) {
        MyPrintf_DMA("%4d", Gray_Anolog[i]);
    }
    MyPrintf_DMA("\r\n");

    // 输出归一化值
    MyPrintf_DMA("Normal : ");
    for(int i = 0; i < 8; i++) {
        MyPrintf_DMA("%4d", Gray_Normal[i]);
    }
    MyPrintf_DMA("\r\n");

    // 输出传感器状态解释（0=检测到黑线，1=白色背景）
    MyPrintf_DMA("Status : ");
    for(int i = 0; i < 8; i++) {
        if((Gray_Digtal >> i) & 1) {
            MyPrintf_DMA("  W "); // White 白色/高电平
        } else {
            MyPrintf_DMA("  B "); // Black 黑线/低电平
        }
    }
    MyPrintf_DMA("\r\n");

    // 输出白色和黑色阈值
    MyPrintf_DMA("W_Thre : ");
    for(int i = 0; i < 8; i++) {
        MyPrintf_DMA("%4d", GraySensor.Gray_white[i]);
    }
    MyPrintf_DMA("\r\n");

    MyPrintf_DMA("B_Thre : ");
    for(int i = 0; i < 8; i++) {
        MyPrintf_DMA("%4d", GraySensor.Gray_black[i]);
    }
    MyPrintf_DMA("\r\n");

    MyPrintf_DMA("========================\r\n");
}

/**
 * @brief 简化版灰度传感器调试输出
 * 输出简洁的传感器状态信息
 */
void Debug_GraySensor_Simple(void)
{
    if(!GraySensor.ok) {
        MyPrintf_DMA("Sensor not ready!\r\n");
        return;
    }

    // 更新数据
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);
    Get_Anolog_Value(&GraySensor, Gray_Anolog);

    // 输出传感器状态（0=黑线，1=白色）
    MyPrintf_DMA("Sensors[0-7]: ");
    for(int i = 0; i < 8; i++) {
        if((Gray_Digtal >> i) & 1) {
            MyPrintf_DMA("W"); // White 白色
        } else {
            MyPrintf_DMA("B"); // Black 黑线
        }
    }
    MyPrintf_DMA(" | ADC: ");
    for(int i = 0; i < 8; i++) {
        MyPrintf_DMA("%d ", Gray_Anolog[i]);
    }
    MyPrintf_DMA("\r\n");
}

/**
 * @brief 测试单个传感器通道的ADC值
 * @param channel 传感器通道 (0-7)
 */
void Debug_Single_Channel(uint8_t channel)
{
    if(channel > 7) {
        MyPrintf_DMA("Invalid channel: %d (0-7)\r\n", channel);
        return;
    }

    // 设置地址线选择对应通道
    Switch_Address_0(!(channel&0x01));
    Switch_Address_1(!(channel&0x02));
    Switch_Address_2(!(channel&0x04));

    // 读取ADC值
    uint16_t adc_val = adc_getValue();

    MyPrintf_DMA("Channel %d: ADC=%d", channel, adc_val);

    // 判断是否为黑线
    if(GraySensor.ok) {
        if(adc_val < GraySensor.Gray_black[channel]) {
            MyPrintf_DMA(" (BLACK)");
        } else if(adc_val > GraySensor.Gray_white[channel]) {
            MyPrintf_DMA(" (WHITE)");
        } else {
            MyPrintf_DMA(" (GRAY)");
        }
    }
    MyPrintf_DMA("\r\n");
}

/**
 * @brief 灰度传感器测试任务 - 可替换Task_GraySensor用于调试
 * 每次调用输出当前传感器状态
 */
void Task_GraySensor_Test(void *para)
{
    // 执行传感器数据采集
    No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);
    Gray_Digtal = Get_Digtal_For_User(&GraySensor);
    Get_Anolog_Value(&GraySensor, Gray_Anolog);
    Get_Normalize_For_User(&GraySensor, Gray_Normal);

    // 每次都输出简化调试信息
    Debug_GraySensor_Simple();
}

/**
 * @brief 串口测试任务 - 用于验证串口是否正常工作
 */
void Task_Serial_Test(void *para)
{
    static uint32_t test_cnt = 0;
    test_cnt++;

    // 使用阻塞式发送测试
    MyPrintf("Serial Test %lu - Blocking\r\n", test_cnt);

    // 使用DMA发送测试
    MyPrintf_DMA("Serial Test %lu - DMA\r\n", test_cnt);

    // 测试传感器状态
    if(GraySensor.ok) {
        MyPrintf_DMA("GraySensor OK\r\n");
    } else {
        MyPrintf_DMA("GraySensor NOT READY\r\n");
    }
}