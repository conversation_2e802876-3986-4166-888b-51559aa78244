#include "Tracker.h"

#define DIS_INRERVAL _IQ(1.5) //传感器间距

/**
 * @brief 读取循迹传感器并计算位置偏差
 * 
 * @param tck_ptr 8路传感器数据数组指针
 * @param offset_ptr 位置偏差值指针 (单位:cm, 负值表示偏左，正值表示偏右)
 * @return true 成功读取并计算偏差 
 * @return false 读取失败或参数错误
 */
bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr)
{
    if (tck_ptr == NULL || offset_ptr == NULL) return false;

    /*获取循迹传感器的值*/
    tck_ptr[0] = DL_GPIO_readPins(Tracker_PORT, Tracker__1_PIN);
    tck_ptr[1] = DL_GPIO_readPins(Tracker_PORT, Tracker__2_PIN);
    tck_ptr[2] = DL_GPIO_readPins(Tracker_PORT, Tracker__3_PIN);
    tck_ptr[3] = DL_GPIO_readPins(Tracker_PORT, Tracker__4_PIN);
    tck_ptr[4] = DL_GPIO_readPins(Tracker_PORT, Tracker__5_PIN);
    tck_ptr[5] = DL_GPIO_readPins(Tracker_PORT, Tracker__6_PIN);
    tck_ptr[6] = DL_GPIO_readPins(Tracker_PORT, Tracker__7_PIN);
    tck_ptr[7] = DL_GPIO_readPins(Tracker_PORT, Tracker__8_PIN);

    /*计算位置偏差*/
    _iq pos_sum = _IQ(0);
    uint8_t cnt = 0;

    // 加权平均法计算位置
    for (uint8_t i = 0; i < 8; i++)
    {
        if (tck_ptr[i] == TRACK_ON)
        {
            // 传感器位置权重: 0,1,2,3,4,5,6,7 对应 -5.25,-3.75,-2.25,-0.75,0.75,2.25,3.75,5.25 cm
            _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INRERVAL);
            pos_sum += sensor_pos;
            cnt++;
        }
    }

    if (cnt == 0)
    {
        // 没有检测到线，保持上次偏差值
        return false;
    }

    // 计算加权平均位置偏差
    *offset_ptr = _IQdiv(pos_sum, _IQ(cnt));

    return true;
}