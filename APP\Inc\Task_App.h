#ifndef __Task_App_h
#define __Task_App_h

#include "SysConfig.h"

extern int16_t Data_MotorEncoder[4];
extern No_MCU_Sensor GraySensor; //灰度传感器实例

void Task_Init(void);
void Task_Encoder_Debug(void *para); //编码器调试输出任务
void Task_GraySensor(void *para); //灰度传感器任务
void Debug_GraySensor_Output(void); //灰度传感器详细调试输出
void Debug_GraySensor_Simple(void); //灰度传感器简化调试输出
void Debug_Single_Channel(uint8_t channel); //单通道ADC测试
void Task_GraySensor_Test(void *para); //灰度传感器测试任务

#endif